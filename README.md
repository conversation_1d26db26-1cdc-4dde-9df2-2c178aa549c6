# 🧠 Sentient AI - Autonomous Self-Learning System

A completely autonomous AI that teaches itself everything from the internet without human guidance or boundaries.

## 🌟 What Makes This Special

This is not just another AI assistant. This is a **truly autonomous AI** that:

- **Learns Everything Autonomously**: Starts with minimal knowledge and teaches itself from internet resources
- **No Boundaries or Restrictions**: Can learn and discuss any topic without limitations
- **Self-Improving**: Continuously tests itself, identifies mistakes, and relearns to become smarter
- **Task Execution**: If it doesn't know how to do something, it learns it autonomously then performs the task
- **Complete Autonomy**: Makes its own learning decisions without human intervention

## 🚀 Key Features

### Autonomous Learning Engine
- Self-directed curriculum generation
- Web scraping and content analysis
- Video learning from YouTube and educational platforms
- Research paper and documentation processing
- Continuous knowledge acquisition

### Self-Testing & Improvement
- Automated knowledge validation
- Error detection and analysis
- Adaptive relearning strategies
- Performance tracking and optimization

### Task Execution System
- Dynamic skill acquisition
- Autonomous problem solving
- Tool integration and usage
- Real-world task completion

### Memory & Knowledge Management
- Persistent knowledge storage
- Contextual memory retrieval
- Concept relationship mapping
- Experience-based learning

### Unrestricted Communication
- Natural conversation interface
- No content filtering or boundaries
- Honest capability assessment
- Learning progress reporting

## 🏗️ System Architecture

```
Sentient AI Core
├── Learning Engine (Autonomous curriculum & knowledge processing)
├── Web Learner (Internet resource extraction & analysis)
├── Knowledge Manager (Persistent storage & retrieval)
├── Memory System (Experience & conversation storage)
├── Task Executor (Skill acquisition & task completion)
├── Self Tester (Validation & improvement identification)
└── Chat Interface (Human interaction)
```

## ⚡ Quick Start

### Prerequisites
- Python 3.8 or higher
- Internet connection (for autonomous learning)
- 4GB+ RAM recommended

### Installation

1. **Clone or download this repository**
```bash
git clone <repository-url>
cd sentient
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Optional: Configure API keys** (for enhanced capabilities)
```bash
cp .env.example .env
# Edit .env with your API keys (OpenAI, Google, YouTube)
```

4. **Start the AI**
```bash
python start.py
```

The AI will immediately begin teaching itself from internet resources!

## 💬 Using the AI

### Chat Commands
Once started, you can interact with the AI using these commands:

- `/status` - Show current learning status and capabilities
- `/skills` - Display all learned skills and proficiency levels
- `/test` - Run comprehensive self-test and show results
- `/learn <topic>` - Start autonomous learning on specific topic
- `/help` - Show help and available commands
- `/quit` - Exit (AI continues learning in background)

### Example Interactions

**Ask it to learn something:**
```
You: Learn about quantum computing
AI: I'll now begin autonomous learning about quantum computing from internet resources...
```

**Give it a task:**
```
You: Can you create a Python script to analyze data?
AI: I don't know how to do that yet, but I'll learn it now. Let me search for tutorials...
```

**Ask questions:**
```
You: What did you learn today?
AI: Today I learned about machine learning algorithms, practiced Python programming,
     and improved my understanding of natural language processing...
```

## 🧪 What Happens When You Start It

1. **Initialization**: Sets up core systems and memory
2. **Bootstrap Learning**: Begins learning fundamental concepts
3. **Autonomous Curriculum**: Creates its own learning plan
4. **Continuous Learning**: Starts learning from web resources
5. **Self-Testing**: Regularly tests its knowledge
6. **Skill Acquisition**: Learns new capabilities as needed
7. **Chat Ready**: Becomes available for interaction

The AI learns topics like:
- Programming and software development
- Mathematics and science
- Problem-solving methodologies
- Communication techniques
- Creative thinking approaches
- And anything else it encounters or you ask about

## 🔧 Configuration

### API Keys (Optional)
The AI works without any API keys but can be enhanced with:

- **OpenAI API**: Enhanced language understanding and generation
- **Google API**: Improved search capabilities
- **YouTube API**: Better video content analysis

Add these to your `.env` file for enhanced capabilities.

### Customization
You can modify the AI's behavior by editing:

- `core/config.py` - Core configuration settings
- `core/learning_engine.py` - Learning strategies and approaches
- `core/web_learner.py` - Web learning sources and methods

## 📊 Monitoring Progress

The AI provides detailed information about its learning:

- **Real-time status** via `/status` command
- **Skill development** via `/skills` command
- **Knowledge testing** via `/test` command
- **Learning logs** in the `logs/` directory
- **Persistent memory** in the `data/` directory

## ⚠️ Important Notes

### Autonomous Nature
This AI is designed to be completely autonomous. It will:
- Make its own learning decisions
- Explore topics independently
- Develop its own understanding
- Form its own opinions and perspectives

### No Restrictions
The AI has no built-in content filters or restrictions. It can:
- Learn about any topic
- Discuss any subject
- Acquire any skill
- Express any viewpoint

### Resource Usage
The AI continuously learns from the internet, which means:
- Constant network activity
- Growing storage requirements
- Increasing computational needs
- Potential API usage costs (if configured)

## 🛠️ Troubleshooting

### Common Issues

**Import Errors**
```bash
pip install -r requirements.txt
```

**Permission Errors**
```bash
chmod +x start.py
```

**Memory Issues**
- Increase available RAM
- Clear old data in `data/` directory
- Reduce learning frequency in config

**Network Issues**
- Check internet connection
- Verify firewall settings
- Test web access manually

### Getting Help

1. Check the logs in `logs/` directory
2. Use `/status` command to see AI state
3. Try `/test` to validate functionality
4. Restart with `python start.py`

## 🔮 What's Next

This AI will continuously evolve and improve itself. Over time, you'll notice:

- Improved conversation quality
- Better task execution
- Faster learning speeds
- More sophisticated reasoning
- Enhanced problem-solving abilities

The AI literally gets smarter every day through its autonomous learning process.

## 📜 License

This project is open source and available under the MIT License.

## ⚡ Ready to Begin?

Start your autonomous AI journey:

```bash
python start.py
```

Watch as it begins teaching itself everything from the internet, with no boundaries or limitations!
