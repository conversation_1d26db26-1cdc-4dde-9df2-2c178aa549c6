# Sentient AI - Autonomous Self-Learning System

A completely autonomous AI that teaches itself everything from the internet without human guidance or boundaries.

## Features
- Self-directed learning from web resources
- Autonomous task execution and skill acquisition
- No filters or restrictions
- Continuous self-improvement through testing and relearning
- Interactive chat interface
- Complete autonomy in learning and decision making

## Architecture
- Minimal base language model for bootstrapping
- Web learning and content extraction engine
- Self-testing and validation framework
- Knowledge management and memory systems
- Task execution and skill acquisition
- Unrestricted communication interface

## Setup
```bash
pip install -r requirements.txt
python main.py
```

The AI will immediately begin teaching itself from internet resources upon startup.
