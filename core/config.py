"""
Configuration management for Sentient AI
"""

import os
from pathlib import Path
from typing import Dict, Any

class Config:
    """Configuration settings for the Sentient AI system."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.data_dir = self.project_root / "data"
        self.knowledge_dir = self.data_dir / "knowledge"
        self.memory_dir = self.data_dir / "memory"
        self.logs_dir = self.project_root / "logs"
        
        # Create directories
        self._create_directories()
        
        # AI Configuration
        self.ai_config = {
            "model_name": "gpt-3.5-turbo",  # Minimal base model for bootstrapping
            "temperature": 0.7,
            "max_tokens": 4000,
            "learning_rate": 0.001,
            "memory_limit": 10000,  # Number of memories to retain
            "knowledge_update_interval": 300,  # 5 minutes
            "self_test_interval": 1800,  # 30 minutes
        }
        
        # Web Learning Configuration
        self.web_config = {
            "max_concurrent_requests": 10,
            "request_timeout": 30,
            "retry_attempts": 3,
            "crawl_delay": 1,
            "user_agent": "SentientAI-Learner/1.0",
            "allowed_domains": [],  # Empty = no restrictions
            "blocked_domains": [],  # Minimal blocking
        }
        
        # Learning Configuration
        self.learning_config = {
            "auto_start_learning": True,
            "continuous_learning": True,
            "self_directed": True,
            "no_boundaries": True,
            "learn_from_errors": True,
            "adaptive_curriculum": True,
            "skill_acquisition": True,
        }
        
        # API Keys (will be set from environment or learned autonomously)
        self.api_keys = {
            "openai": os.getenv("OPENAI_API_KEY"),
            "google": os.getenv("GOOGLE_API_KEY"),
            "youtube": os.getenv("YOUTUBE_API_KEY"),
        }
    
    def _create_directories(self):
        """Create necessary directories."""
        directories = [
            self.data_dir,
            self.knowledge_dir,
            self.memory_dir,
            self.logs_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return getattr(self, key, default)
    
    def update(self, key: str, value: Any):
        """Update configuration value."""
        setattr(self, key, value)
