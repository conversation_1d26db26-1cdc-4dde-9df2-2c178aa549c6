"""
Knowledge Management System for Sentient AI
Stores, organizes, and retrieves learned knowledge.
"""

import asyncio
import json
import logging
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import pickle

class KnowledgeManager:
    """
    Manages the AI's knowledge base with efficient storage and retrieval.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Knowledge storage
        self.knowledge_base = {}
        self.topic_hierarchy = {}
        self.concept_relationships = {}
        
        # Database for persistent storage
        self.db_path = self.config.knowledge_dir / "knowledge.db"
        self.db_connection = None
        
        # Knowledge categories
        self.categories = {
            "factual": "Concrete facts and information",
            "procedural": "How-to knowledge and procedures",
            "conceptual": "Abstract concepts and theories",
            "metacognitive": "Knowledge about learning and thinking",
            "experiential": "Knowledge gained from experience"
        }
    
    async def initialize(self):
        """Initialize the knowledge management system."""
        self.logger.info("📚 Initializing knowledge management system...")
        
        # Setup database
        await self._setup_database()
        
        # Load existing knowledge
        await self._load_knowledge()
        
        self.logger.info("✅ Knowledge management system initialized")
    
    async def _setup_database(self):
        """Setup SQLite database for knowledge storage."""
        self.db_connection = sqlite3.connect(str(self.db_path))
        cursor = self.db_connection.cursor()
        
        # Create knowledge table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                category TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT,
                confidence REAL DEFAULT 0.5,
                importance REAL DEFAULT 0.5,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                access_count INTEGER DEFAULT 0,
                tags TEXT
            )
        ''')
        
        # Create concepts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS concepts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                definition TEXT,
                category TEXT,
                related_concepts TEXT,
                examples TEXT,
                created_at TEXT NOT NULL,
                mastery_level REAL DEFAULT 0.0
            )
        ''')
        
        # Create relationships table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                concept1 TEXT NOT NULL,
                concept2 TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                strength REAL DEFAULT 0.5,
                created_at TEXT NOT NULL
            )
        ''')
        
        # Create skills table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS skills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                category TEXT,
                prerequisites TEXT,
                steps TEXT,
                proficiency REAL DEFAULT 0.0,
                last_practiced TEXT,
                success_rate REAL DEFAULT 0.0
            )
        ''')
        
        self.db_connection.commit()
    
    async def add_knowledge(self, topic: str, knowledge_data: Dict[str, Any]):
        """Add new knowledge to the knowledge base."""
        try:
            # Determine knowledge category
            category = await self._categorize_knowledge(knowledge_data)
            
            # Extract and process content
            content = knowledge_data.get("content", "")
            source = knowledge_data.get("source_url", "unknown")
            
            # Calculate confidence and importance
            confidence = await self._calculate_confidence(knowledge_data)
            importance = await self._calculate_importance(knowledge_data)
            
            # Store in database
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO knowledge (topic, category, content, source, confidence, importance, created_at, updated_at, tags)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                topic,
                category,
                json.dumps(knowledge_data),
                source,
                confidence,
                importance,
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                json.dumps(knowledge_data.get("tags", []))
            ))
            self.db_connection.commit()
            
            # Extract and store concepts
            await self._extract_and_store_concepts(topic, knowledge_data)
            
            # Update in-memory knowledge base
            if topic not in self.knowledge_base:
                self.knowledge_base[topic] = []
            
            self.knowledge_base[topic].append({
                "content": knowledge_data,
                "category": category,
                "confidence": confidence,
                "importance": importance,
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.debug(f"Added knowledge for topic: {topic}")
            
        except Exception as e:
            self.logger.error(f"Error adding knowledge: {e}")
    
    async def update_knowledge(self, topic: str, new_knowledge: Dict[str, Any]):
        """Update existing knowledge with new information."""
        try:
            # Find existing knowledge
            existing = await self.get_knowledge(topic)
            
            if existing:
                # Merge with existing knowledge
                merged_knowledge = await self._merge_knowledge(existing, new_knowledge)
                
                # Update in database
                cursor = self.db_connection.cursor()
                cursor.execute('''
                    UPDATE knowledge 
                    SET content = ?, updated_at = ?, confidence = ?
                    WHERE topic = ?
                ''', (
                    json.dumps(merged_knowledge),
                    datetime.now().isoformat(),
                    await self._calculate_confidence(merged_knowledge),
                    topic
                ))
                self.db_connection.commit()
                
                # Update in-memory
                if topic in self.knowledge_base:
                    self.knowledge_base[topic][-1]["content"] = merged_knowledge
                    self.knowledge_base[topic][-1]["timestamp"] = datetime.now().isoformat()
            else:
                # Add as new knowledge
                await self.add_knowledge(topic, new_knowledge)
            
        except Exception as e:
            self.logger.error(f"Error updating knowledge: {e}")
    
    async def get_knowledge(self, topic: str) -> Optional[Dict[str, Any]]:
        """Retrieve knowledge for a specific topic."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT content, confidence, importance, created_at, updated_at
                FROM knowledge
                WHERE topic = ?
                ORDER BY importance DESC, updated_at DESC
                LIMIT 1
            ''', (topic,))
            
            result = cursor.fetchone()
            if result:
                # Update access count
                cursor.execute('''
                    UPDATE knowledge 
                    SET access_count = access_count + 1
                    WHERE topic = ?
                ''', (topic,))
                self.db_connection.commit()
                
                return {
                    "content": json.loads(result[0]),
                    "confidence": result[1],
                    "importance": result[2],
                    "created_at": result[3],
                    "updated_at": result[4]
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error retrieving knowledge: {e}")
            return None
    
    async def get_relevant_knowledge(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get knowledge relevant to a query."""
        try:
            cursor = self.db_connection.cursor()
            
            # Search in topic, content, and tags
            cursor.execute('''
                SELECT topic, content, confidence, importance, source
                FROM knowledge
                WHERE topic LIKE ? OR content LIKE ? OR tags LIKE ?
                ORDER BY importance DESC, confidence DESC
                LIMIT ?
            ''', (f"%{query}%", f"%{query}%", f"%{query}%", limit))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    "topic": row[0],
                    "content": json.loads(row[1]),
                    "confidence": row[2],
                    "importance": row[3],
                    "source": row[4]
                })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting relevant knowledge: {e}")
            return []
    
    async def add_concept(self, name: str, definition: str, category: str = "general"):
        """Add a new concept to the knowledge base."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO concepts (name, definition, category, created_at)
                VALUES (?, ?, ?, ?)
            ''', (name, definition, category, datetime.now().isoformat()))
            self.db_connection.commit()
            
            self.logger.debug(f"Added concept: {name}")
            
        except Exception as e:
            self.logger.error(f"Error adding concept: {e}")
    
    async def add_relationship(self, concept1: str, concept2: str, relationship_type: str, strength: float = 0.5):
        """Add a relationship between concepts."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO relationships (concept1, concept2, relationship_type, strength, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (concept1, concept2, relationship_type, strength, datetime.now().isoformat()))
            self.db_connection.commit()
            
        except Exception as e:
            self.logger.error(f"Error adding relationship: {e}")
    
    async def add_task_knowledge(self, task_name: str, instructions: Dict[str, Any]):
        """Add knowledge about how to perform a task."""
        try:
            cursor = self.db_connection.cursor()
            
            # Store as skill
            cursor.execute('''
                INSERT OR REPLACE INTO skills (name, description, steps, category)
                VALUES (?, ?, ?, ?)
            ''', (
                task_name,
                instructions.get("description", ""),
                json.dumps(instructions.get("steps", [])),
                "task_execution"
            ))
            self.db_connection.commit()
            
            # Also store as general knowledge
            await self.add_knowledge(f"task_{task_name}", {
                "type": "task_instructions",
                "instructions": instructions,
                "task_name": task_name
            })
            
        except Exception as e:
            self.logger.error(f"Error adding task knowledge: {e}")
    
    async def get_task_knowledge(self, task_name: str) -> Optional[Dict[str, Any]]:
        """Get knowledge about how to perform a task."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT description, steps, proficiency, success_rate
                FROM skills
                WHERE name = ?
            ''', (task_name,))
            
            result = cursor.fetchone()
            if result:
                return {
                    "description": result[0],
                    "steps": json.loads(result[1]) if result[1] else [],
                    "proficiency": result[2],
                    "success_rate": result[3]
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting task knowledge: {e}")
            return None
    
    async def consolidate_learning(self):
        """Consolidate and organize learned knowledge."""
        try:
            # Update concept relationships based on co-occurrence
            await self._update_concept_relationships()
            
            # Identify knowledge gaps
            await self._identify_knowledge_gaps()
            
            # Update importance scores based on access patterns
            await self._update_importance_scores()
            
            self.logger.info("📊 Knowledge consolidation completed")
            
        except Exception as e:
            self.logger.error(f"Error consolidating knowledge: {e}")
    
    async def _categorize_knowledge(self, knowledge_data: Dict[str, Any]) -> str:
        """Categorize knowledge into appropriate category."""
        content = str(knowledge_data.get("content", "")).lower()
        
        # Simple categorization based on content
        if any(word in content for word in ["how to", "step", "procedure", "method"]):
            return "procedural"
        elif any(word in content for word in ["concept", "theory", "principle", "idea"]):
            return "conceptual"
        elif any(word in content for word in ["fact", "data", "statistic", "number"]):
            return "factual"
        elif any(word in content for word in ["learning", "thinking", "strategy", "approach"]):
            return "metacognitive"
        else:
            return "experiential"
    
    async def _calculate_confidence(self, knowledge_data: Dict[str, Any]) -> float:
        """Calculate confidence score for knowledge."""
        confidence = 0.5  # Base confidence
        
        # Increase confidence for authoritative sources
        source = knowledge_data.get("source_url", "").lower()
        if any(domain in source for domain in ["wikipedia", "edu", "gov", "stackoverflow"]):
            confidence += 0.2
        
        # Increase confidence for detailed content
        content_length = len(str(knowledge_data.get("content", "")))
        if content_length > 1000:
            confidence += 0.1
        
        # Increase confidence for structured content
        if knowledge_data.get("key_points") or knowledge_data.get("steps"):
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    async def _calculate_importance(self, knowledge_data: Dict[str, Any]) -> float:
        """Calculate importance score for knowledge."""
        importance = 0.5  # Base importance
        
        # Increase importance for fundamental concepts
        content = str(knowledge_data.get("content", "")).lower()
        if any(word in content for word in ["fundamental", "basic", "essential", "important"]):
            importance += 0.2
        
        # Increase importance for practical knowledge
        if any(word in content for word in ["practical", "useful", "application", "example"]):
            importance += 0.1
        
        # Increase importance for learning-related content
        if any(word in content for word in ["learn", "understand", "master", "skill"]):
            importance += 0.1
        
        return min(importance, 1.0)
    
    async def _extract_and_store_concepts(self, topic: str, knowledge_data: Dict[str, Any]):
        """Extract concepts from knowledge and store them."""
        concepts = knowledge_data.get("concepts", [])
        
        for concept in concepts:
            if isinstance(concept, str) and len(concept.strip()) > 2:
                await self.add_concept(
                    name=concept.strip(),
                    definition=f"Concept from {topic}",
                    category=await self._categorize_knowledge(knowledge_data)
                )
    
    async def _merge_knowledge(self, existing: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """Merge new knowledge with existing knowledge."""
        merged = existing["content"].copy()
        
        # Merge content
        if "content" in new:
            merged["content"] = merged.get("content", "") + "\n\n" + new["content"]
        
        # Merge key points
        if "key_points" in new:
            existing_points = merged.get("key_points", [])
            new_points = new["key_points"]
            merged["key_points"] = existing_points + [p for p in new_points if p not in existing_points]
        
        # Merge concepts
        if "concepts" in new:
            existing_concepts = merged.get("concepts", [])
            new_concepts = new["concepts"]
            merged["concepts"] = existing_concepts + [c for c in new_concepts if c not in existing_concepts]
        
        return merged
    
    async def _update_concept_relationships(self):
        """Update relationships between concepts based on co-occurrence."""
        # This would analyze which concepts appear together frequently
        # and create or strengthen relationships between them
        pass
    
    async def _identify_knowledge_gaps(self):
        """Identify gaps in the knowledge base."""
        # This would analyze the knowledge base to find areas that need more learning
        pass
    
    async def _update_importance_scores(self):
        """Update importance scores based on access patterns."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                UPDATE knowledge 
                SET importance = importance + (access_count * 0.01)
                WHERE access_count > 0
            ''')
            self.db_connection.commit()
        except Exception as e:
            self.logger.error(f"Error updating importance scores: {e}")
    
    async def _load_knowledge(self):
        """Load existing knowledge from database into memory."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT topic, content, category, confidence, importance
                FROM knowledge
                ORDER BY importance DESC
                LIMIT 1000
            ''')
            
            for row in cursor.fetchall():
                topic = row[0]
                if topic not in self.knowledge_base:
                    self.knowledge_base[topic] = []
                
                self.knowledge_base[topic].append({
                    "content": json.loads(row[1]),
                    "category": row[2],
                    "confidence": row[3],
                    "importance": row[4]
                })
            
            self.logger.info(f"Loaded {len(self.knowledge_base)} topics into memory")
            
        except Exception as e:
            self.logger.error(f"Error loading knowledge: {e}")
    
    def __del__(self):
        """Cleanup database connection."""
        if self.db_connection:
            self.db_connection.close()
