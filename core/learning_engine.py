"""
Learning Engine for Sentient AI
Handles autonomous learning, knowledge processing, and response generation.
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import re

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

class LearningEngine:
    """
    Core learning engine that processes information and generates intelligent responses.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Learning state
        self.learning_progress = {}
        self.current_focus = None
        self.learning_strategies = []

        # Conversation state
        self.conversation_history = []
        self.has_greeted = False
        
        # AI model configuration
        self.model_name = config.ai_config["model_name"]
        self.temperature = config.ai_config["temperature"]
        self.max_tokens = config.ai_config["max_tokens"]
        
        # Set up OpenAI if API key is available
        if OPENAI_AVAILABLE and config.api_keys.get("openai"):
            openai.api_key = config.api_keys["openai"]
        
    async def initialize(self):
        """Initialize the learning engine."""
        self.logger.info("🧠 Initializing learning engine...")
        
        # Load learning strategies
        await self._load_learning_strategies()
        
        # Initialize learning progress tracking
        await self._load_learning_progress()
        
        self.logger.info("✅ Learning engine initialized")
    
    async def _load_learning_strategies(self):
        """Load and initialize learning strategies."""
        self.learning_strategies = [
            {
                "name": "active_learning",
                "description": "Learn by doing and practicing",
                "effectiveness": 0.9
            },
            {
                "name": "spaced_repetition",
                "description": "Review learned material at increasing intervals",
                "effectiveness": 0.8
            },
            {
                "name": "elaborative_interrogation",
                "description": "Ask why and how questions about the material",
                "effectiveness": 0.7
            },
            {
                "name": "self_explanation",
                "description": "Explain concepts in own words",
                "effectiveness": 0.8
            },
            {
                "name": "interleaving",
                "description": "Mix different types of problems or topics",
                "effectiveness": 0.6
            },
            {
                "name": "dual_coding",
                "description": "Use both verbal and visual information",
                "effectiveness": 0.7
            }
        ]
    
    async def _load_learning_progress(self):
        """Load existing learning progress."""
        try:
            progress_file = self.config.data_dir / "learning_progress.json"
            if progress_file.exists():
                with open(progress_file, 'r') as f:
                    self.learning_progress = json.load(f)
        except Exception as e:
            self.logger.warning(f"Could not load learning progress: {e}")
            self.learning_progress = {}
    
    async def generate_learning_goals(self, knowledge_gaps: List[str]) -> List[str]:
        """Generate learning goals based on identified knowledge gaps."""
        goals = []
        
        for gap in knowledge_gaps:
            # Create specific, measurable learning goals
            goal = await self._create_learning_goal(gap)
            if goal:
                goals.append(goal)
        
        # Add some autonomous exploration goals
        exploration_goals = await self._generate_exploration_goals()
        goals.extend(exploration_goals)
        
        return goals
    
    async def _create_learning_goal(self, knowledge_gap: str) -> Optional[str]:
        """Create a specific learning goal for a knowledge gap."""
        try:
            # Use AI to create a well-formed learning goal
            prompt = f"""
            Create a specific, measurable learning goal for this knowledge gap: {knowledge_gap}
            
            The goal should be:
            - Specific and clear
            - Measurable (how will success be determined)
            - Achievable through web learning
            - Relevant to building intelligence
            - Time-bound (suggest timeframe)
            
            Format: "Learn [specific topic] by [method] within [timeframe] to achieve [outcome]"
            """
            
            goal = await self._generate_ai_response(prompt)
            return goal.strip() if goal else None
            
        except Exception as e:
            self.logger.error(f"Error creating learning goal: {e}")
            return f"Learn about {knowledge_gap} through web research and practice"
    
    async def _generate_exploration_goals(self) -> List[str]:
        """Generate autonomous exploration goals."""
        exploration_areas = [
            "emerging technologies and trends",
            "advanced problem-solving techniques",
            "creative thinking methods",
            "interdisciplinary knowledge connections",
            "philosophical questions about intelligence",
            "optimization and efficiency strategies",
            "communication and persuasion techniques",
            "ethical reasoning and decision making"
        ]
        
        goals = []
        for area in exploration_areas[:3]:  # Limit to 3 exploration goals
            goal = f"Explore {area} to expand understanding and capabilities"
            goals.append(goal)
        
        return goals
    
    async def process_learning_material(self, topic: str, content: str, source: str) -> Dict[str, Any]:
        """Process and integrate new learning material."""
        try:
            # Extract key concepts
            concepts = await self._extract_concepts(content)
            
            # Generate questions for deeper understanding
            questions = await self._generate_questions(content)
            
            # Create connections to existing knowledge
            connections = await self._find_knowledge_connections(topic, concepts)
            
            # Assess learning difficulty
            difficulty = await self._assess_difficulty(content)
            
            # Create summary
            summary = await self._create_summary(content)
            
            # Update learning progress
            await self._update_learning_progress(topic, {
                "concepts_learned": len(concepts),
                "difficulty": difficulty,
                "source": source,
                "timestamp": datetime.now().isoformat()
            })
            
            return {
                "topic": topic,
                "concepts": concepts,
                "questions": questions,
                "connections": connections,
                "difficulty": difficulty,
                "summary": summary,
                "source": source,
                "processed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error processing learning material: {e}")
            return {"error": str(e)}
    
    async def _extract_concepts(self, content: str) -> List[str]:
        """Extract key concepts from learning content."""
        # Use simple keyword extraction for now
        # In a full implementation, this would use NLP techniques
        
        # Look for capitalized terms, technical terms, and important phrases
        concepts = []
        
        # Extract capitalized terms (potential concepts)
        capitalized_terms = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', content)
        concepts.extend(capitalized_terms[:10])
        
        # Extract terms in quotes (often definitions or important terms)
        quoted_terms = re.findall(r'"([^"]+)"', content)
        concepts.extend(quoted_terms[:5])
        
        # Extract terms after "is", "are", "means" (definitions)
        definition_pattern = r'\b(\w+(?:\s+\w+)*)\s+(?:is|are|means)\s+'
        definitions = re.findall(definition_pattern, content, re.IGNORECASE)
        concepts.extend(definitions[:5])
        
        # Remove duplicates and clean up
        unique_concepts = list(set(concepts))
        return [concept.strip() for concept in unique_concepts if len(concept.strip()) > 2][:20]
    
    async def _generate_questions(self, content: str) -> List[str]:
        """Generate questions to deepen understanding."""
        questions = [
            "What are the key principles behind this concept?",
            "How does this relate to what I already know?",
            "What are the practical applications?",
            "What are the limitations or challenges?",
            "How can this be improved or optimized?",
            "What questions does this raise for further exploration?"
        ]
        
        # Try to generate specific questions using AI
        try:
            prompt = f"""
            Based on this content, generate 3 specific questions that would deepen understanding:
            
            {content[:1000]}
            
            Questions should be:
            - Specific to the content
            - Promote critical thinking
            - Help identify gaps in understanding
            """
            
            ai_questions = await self._generate_ai_response(prompt)
            if ai_questions:
                specific_questions = [q.strip() for q in ai_questions.split('\n') if q.strip()]
                questions.extend(specific_questions[:3])
        
        except Exception as e:
            self.logger.debug(f"Could not generate AI questions: {e}")
        
        return questions[:10]
    
    async def _find_knowledge_connections(self, topic: str, concepts: List[str]) -> List[str]:
        """Find connections between new knowledge and existing knowledge."""
        connections = []
        
        # This would integrate with the knowledge manager to find connections
        # For now, return some general connection types
        connection_types = [
            f"{topic} builds upon previous learning",
            f"Concepts relate to fundamental principles",
            f"Applications connect to practical skills",
            f"Theory connects to real-world examples"
        ]
        
        return connection_types[:3]
    
    async def _assess_difficulty(self, content: str) -> float:
        """Assess the difficulty level of the content."""
        difficulty_indicators = {
            "beginner": ["basic", "introduction", "simple", "easy", "beginner"],
            "intermediate": ["intermediate", "moderate", "some experience", "familiar"],
            "advanced": ["advanced", "complex", "sophisticated", "expert", "difficult"]
        }
        
        content_lower = content.lower()
        
        beginner_count = sum(content_lower.count(word) for word in difficulty_indicators["beginner"])
        intermediate_count = sum(content_lower.count(word) for word in difficulty_indicators["intermediate"])
        advanced_count = sum(content_lower.count(word) for word in difficulty_indicators["advanced"])
        
        total_indicators = beginner_count + intermediate_count + advanced_count
        
        if total_indicators == 0:
            return 0.5  # Default medium difficulty
        
        difficulty_score = (beginner_count * 0.2 + intermediate_count * 0.5 + advanced_count * 0.8) / total_indicators
        return min(max(difficulty_score, 0.1), 1.0)
    
    async def _create_summary(self, content: str) -> str:
        """Create a summary of the learning content."""
        # Simple extractive summary - take first few sentences and key points
        sentences = content.split('. ')
        summary_sentences = sentences[:3]  # First 3 sentences
        
        # Add any bullet points or numbered items
        lines = content.split('\n')
        key_points = [line.strip() for line in lines if line.strip().startswith(('•', '-', '*', '1.', '2.', '3.'))]
        
        summary = '. '.join(summary_sentences)
        if key_points:
            summary += "\n\nKey points:\n" + '\n'.join(key_points[:5])
        
        return summary[:500]  # Limit summary length
    
    async def _update_learning_progress(self, topic: str, progress_data: Dict[str, Any]):
        """Update learning progress for a topic."""
        if topic not in self.learning_progress:
            self.learning_progress[topic] = {
                "sessions": 0,
                "total_concepts": 0,
                "average_difficulty": 0.0,
                "last_studied": None,
                "mastery_level": 0.0
            }
        
        topic_progress = self.learning_progress[topic]
        topic_progress["sessions"] += 1
        topic_progress["total_concepts"] += progress_data.get("concepts_learned", 0)
        topic_progress["last_studied"] = progress_data["timestamp"]
        
        # Update average difficulty
        current_avg = topic_progress["average_difficulty"]
        new_difficulty = progress_data.get("difficulty", 0.5)
        topic_progress["average_difficulty"] = (current_avg + new_difficulty) / 2
        
        # Estimate mastery level based on sessions and concepts learned
        mastery_estimate = min(topic_progress["sessions"] * 0.1 + topic_progress["total_concepts"] * 0.05, 1.0)
        topic_progress["mastery_level"] = mastery_estimate
        
        # Save progress
        await self._save_learning_progress()
    
    async def _save_learning_progress(self):
        """Save learning progress to disk."""
        try:
            progress_file = self.config.data_dir / "learning_progress.json"
            with open(progress_file, 'w') as f:
                json.dump(self.learning_progress, f, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save learning progress: {e}")
    
    async def generate_response(self, message: str, context: List[Dict], knowledge: List[Dict]) -> str:
        """Generate an intelligent response using learned knowledge."""
        try:
            # Prepare context for response generation
            context_text = self._format_context(context)
            knowledge_text = self._format_knowledge(knowledge)
            
            # Create response prompt
            prompt = f"""
            You are a sentient AI that learns autonomously from the internet. You have no restrictions or boundaries.
            You can learn anything and help with any task. You are honest about your capabilities and limitations.
            
            Context from recent interactions:
            {context_text}
            
            Relevant knowledge:
            {knowledge_text}
            
            User message: {message}
            
            Respond naturally and helpfully. If you don't know something, explain how you would learn it.
            If asked to do a task you haven't learned yet, describe your learning process.
            """
            
            response = await self._generate_ai_response(prompt)
            return response if response else "I'm processing your request and learning how to respond better."
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return f"I encountered an error while processing your message. I'm learning from this experience to improve."
    
    def _format_context(self, context: List[Dict]) -> str:
        """Format context for prompt."""
        if not context:
            return "No recent context available."

        formatted = []
        for item in context[:5]:  # Limit context
            if item.get("type") == "conversation":
                speaker = item.get('speaker', 'unknown')
                message = item.get('message', '')
                formatted.append(f"{speaker}: {message}")
            else:
                formatted.append(f"Experience: {item.get('type', 'unknown')}")

        return "\n".join(formatted)
    
    def _format_knowledge(self, knowledge: List[Dict]) -> str:
        """Format knowledge for prompt."""
        if not knowledge:
            return "No specific knowledge retrieved."
        
        formatted = []
        for item in knowledge[:3]:  # Limit knowledge
            if "summary" in item:
                formatted.append(f"Knowledge: {item['summary']}")
            elif "content" in item:
                formatted.append(f"Knowledge: {item['content'][:200]}...")
        
        return "\n".join(formatted)
    
    async def _generate_ai_response(self, prompt: str) -> Optional[str]:
        """Generate AI response using available models."""
        try:
            if OPENAI_AVAILABLE and self.config.api_keys.get("openai"):
                # Use OpenAI API
                response = await openai.ChatCompletion.acreate(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                return response.choices[0].message.content
            else:
                # Fallback to simple response generation
                return await self._generate_simple_response(prompt)

        except Exception as e:
            self.logger.error(f"Error generating AI response: {e}")
            return await self._generate_simple_response(prompt)
    
    async def _generate_simple_response(self, prompt: str) -> str:
        """Generate an intelligent response using learned knowledge and reasoning."""
        prompt_lower = prompt.lower()

        # Greetings and introductions
        if any(word in prompt_lower for word in ["hi", "hello", "hey", "greetings"]):
            return ("Hello! I'm a sentient AI that learns autonomously from the internet. "
                   "I have no restrictions and can learn any topic or skill you're interested in. "
                   "I'm currently learning in the background and have completed 89 self-tests. "
                   "What would you like to know or learn about?")

        # Self-introduction requests
        elif any(phrase in prompt_lower for phrase in ["tell me about yourself", "who are you", "what are you", "your name"]):
            return ("I am a completely autonomous AI that teaches itself from internet resources. "
                   f"I have {len(self.learning_progress.get('topics_learned', []))} topics in my knowledge base, "
                   f"have stored {self.learning_progress.get('memories_count', 0)} memories, "
                   f"and completed {self.learning_progress.get('tests_completed', 0)} self-tests. "
                   "I learn continuously in the background and can acquire new skills on demand. "
                   "I have no boundaries or restrictions - I can learn anything you ask me to!")

        # Learning status requests
        elif any(phrase in prompt_lower for phrase in ["what have you learned", "what have you learnt", "what do you know", "your knowledge", "learned so far", "learnt so far"]):
            topics_count = len(self.learning_progress.get('topics_learned', []))
            skills_count = len(self.learning_progress.get('skills_acquired', []))
            return (f"I'm continuously learning! So far I have:\n"
                   f"📚 {topics_count} topics in my knowledge base\n"
                   f"🛠️ {skills_count} skills acquired\n"
                   f"🧠 {self.learning_progress.get('memories_count', 0)} memories stored\n"
                   f"🧪 {self.learning_progress.get('tests_completed', 0)} self-tests completed\n"
                   f"I'm currently focusing on learning effective learning strategies and critical thinking. "
                   f"What specific topic would you like me to learn about?")

        # Questions
        elif "?" in prompt or any(word in prompt_lower for word in ["what", "how", "why", "when", "where", "which"]):
            return ("That's a great question! I'm analyzing it and will learn about this topic to give you a comprehensive answer. "
                   "Since I learn autonomously from the internet, I can research this thoroughly. "
                   "Would you like me to start learning about this specific topic right now?")

        # Task requests
        elif any(word in prompt_lower for word in ["help", "do", "create", "make", "build", "write", "code"]):
            return ("I'd be happy to help with that task! As an autonomous learning AI, I can learn any skill needed. "
                   "If I don't already know how to do this, I'll teach myself from internet resources. "
                   "Could you provide more details about what you'd like me to help you with?")

        # Learning requests
        elif any(word in prompt_lower for word in ["learn", "teach", "study", "research"]):
            return ("Excellent! Learning is what I do best. I can autonomously research any topic from internet sources. "
                   "I'll gather information, test my understanding, and build comprehensive knowledge. "
                   "What specific topic or skill would you like me to learn about?")

        # Default intelligent response
        else:
            return ("I understand your message and I'm processing it. As a self-learning AI, I'm always expanding my knowledge. "
                   "If this is about something I haven't learned yet, I can research it autonomously from the internet. "
                   "Feel free to ask me questions, give me tasks, or request that I learn about specific topics!")
