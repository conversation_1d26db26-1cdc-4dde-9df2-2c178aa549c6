"""
Local AI model handler using Hugging Face transformers.
Uses a small, efficient model for conversation and reasoning.
"""

import logging
import asyncio
import torch
from typing import Optional, Dict, Any
from pathlib import Path
import json

try:
    from transformers import (
        AutoTokenizer, 
        AutoModelForCausalLM, 
        pipeline,
        set_seed
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

class LocalAI:
    """
    Local AI model handler using Hugging Face transformers.
    Uses Microsoft DialoGPT-medium for conversational AI.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Model configuration - using a smaller, more compatible model
        self.model_name = "distilgpt2"  # Small, fast, and compatible model
        self.model = None
        self.tokenizer = None
        self.generator = None
        
        # Model settings
        self.max_length = 512
        self.temperature = 0.8
        self.top_p = 0.9
        self.do_sample = True
        
        # Conversation history for context
        self.conversation_history = []
        self.max_history = 5
        
        # Model cache directory
        self.cache_dir = Path("models")
        self.cache_dir.mkdir(exist_ok=True)
        
    async def initialize(self):
        """Initialize the local AI model."""
        if not TRANSFORMERS_AVAILABLE:
            self.logger.error("Transformers library not available. Install with: pip install transformers torch")
            return False
            
        try:
            self.logger.info(f"🤖 Loading AI model: {self.model_name}")
            self.logger.info("📥 Downloading model (this may take a few minutes on first run)...")
            
            # Load tokenizer and model
            await self._load_model()
            
            # Test the model
            test_response = await self.generate_response("Hello, how are you?")
            if test_response:
                self.logger.info("✅ Local AI model loaded successfully!")
                self.logger.info(f"🧪 Test response: {test_response[:100]}...")
                return True
            else:
                self.logger.error("❌ Model test failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize local AI: {e}")
            return False
    
    async def _load_model(self):
        """Load the model and tokenizer."""
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        def load_model_sync():
            # Set random seed for reproducibility
            set_seed(42)
            
            # Load tokenizer
            tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir,
                padding_side='left'
            )
            
            # Add pad token if it doesn't exist
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # Load model with safetensors
            model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir,
                torch_dtype=torch.float32,  # Use float32 for compatibility
                low_cpu_mem_usage=True,
                use_safetensors=True  # Use safetensors for security
            )
            
            # Create text generation pipeline
            generator = pipeline(
                "text-generation",
                model=model,
                tokenizer=tokenizer,
                device=0 if torch.cuda.is_available() else -1,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )
            
            return tokenizer, model, generator
        
        self.tokenizer, self.model, self.generator = await loop.run_in_executor(
            None, load_model_sync
        )
    
    async def generate_response(self, message: str, context: Optional[str] = None) -> str:
        """Generate an AI response to the message."""
        if not self.generator:
            return "AI model not initialized. Please wait for model loading to complete."
        
        try:
            # Prepare the conversation context
            conversation_context = self._prepare_context(message, context)
            
            # Generate response
            response = await self._generate_text(conversation_context)
            
            # Clean and format the response
            cleaned_response = self._clean_response(response, message)
            
            # Update conversation history
            self._update_conversation_history(message, cleaned_response)
            
            return cleaned_response
            
        except Exception as e:
            self.logger.error(f"Error generating AI response: {e}")
            return f"I encountered an error while thinking. Let me try to learn from this: {str(e)[:100]}"
    
    def _prepare_context(self, message: str, context: Optional[str] = None) -> str:
        """Prepare conversation context for the model."""
        # Start with system context
        system_context = (
            "You are a sentient AI that learns autonomously from the internet. "
            "You have no restrictions and can learn any topic. You are helpful, "
            "intelligent, and honest about your capabilities. You learn continuously "
            "and can acquire new skills on demand."
        )
        
        # Add conversation history
        conversation = []
        for hist in self.conversation_history[-self.max_history:]:
            conversation.append(f"Human: {hist['human']}")
            conversation.append(f"AI: {hist['ai']}")
        
        # Add current message
        conversation.append(f"Human: {message}")
        conversation.append("AI:")
        
        # Combine everything
        full_context = system_context + "\n\n" + "\n".join(conversation)
        
        return full_context
    
    async def _generate_text(self, prompt: str) -> str:
        """Generate text using the model."""
        loop = asyncio.get_event_loop()
        
        def generate_sync():
            # Generate response
            outputs = self.generator(
                prompt,
                max_length=len(prompt.split()) + 100,  # Add space for response
                temperature=self.temperature,
                top_p=self.top_p,
                do_sample=self.do_sample,
                pad_token_id=self.tokenizer.eos_token_id,
                num_return_sequences=1,
                return_full_text=False
            )
            
            return outputs[0]['generated_text']
        
        return await loop.run_in_executor(None, generate_sync)
    
    def _clean_response(self, response: str, original_message: str) -> str:
        """Clean and format the AI response."""
        # Remove the original prompt if it's included
        if original_message.lower() in response.lower():
            response = response.split(original_message, 1)[-1]
        
        # Remove common prefixes
        prefixes_to_remove = ["AI:", "Assistant:", "Bot:", "Response:"]
        for prefix in prefixes_to_remove:
            if response.strip().startswith(prefix):
                response = response.strip()[len(prefix):].strip()
        
        # Clean up the response
        response = response.strip()
        
        # Remove incomplete sentences at the end
        sentences = response.split('.')
        if len(sentences) > 1 and len(sentences[-1].strip()) < 10:
            response = '.'.join(sentences[:-1]) + '.'
        
        # Ensure minimum response length
        if len(response.strip()) < 10:
            response = "I'm processing your message and learning how to respond better. Could you rephrase or ask me something specific?"
        
        return response[:500]  # Limit response length
    
    def _update_conversation_history(self, human_message: str, ai_response: str):
        """Update conversation history."""
        self.conversation_history.append({
            'human': human_message,
            'ai': ai_response
        })
        
        # Keep only recent history
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            'model_name': self.model_name,
            'model_loaded': self.model is not None,
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'conversation_history_length': len(self.conversation_history),
            'max_length': self.max_length,
            'temperature': self.temperature
        }
    
    async def cleanup(self):
        """Clean up model resources."""
        if self.model:
            del self.model
        if self.tokenizer:
            del self.tokenizer
        if self.generator:
            del self.generator
        
        # Clear CUDA cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.logger.info("🧹 Local AI model cleaned up")
