"""
Memory System for Sentient AI
Handles short-term and long-term memory, experiences, and context retrieval.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import sqlite3
import pickle

class MemorySystem:
    """
    Advanced memory system that stores experiences, conversations, and learning.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Memory storage
        self.short_term_memory = []
        self.long_term_memory = []
        self.episodic_memory = []
        self.semantic_memory = {}
        
        # Database for persistent storage
        self.db_path = self.config.memory_dir / "memory.db"
        self.db_connection = None
        
        # Memory limits
        self.short_term_limit = 100
        self.long_term_limit = self.config.ai_config["memory_limit"]
        
    async def initialize(self):
        """Initialize the memory system."""
        self.logger.info("🧠 Initializing memory system...")
        
        # Setup database
        await self._setup_database()
        
        # Load existing memories
        await self._load_memories()
        
        self.logger.info("✅ Memory system initialized")
    
    async def _setup_database(self):
        """Setup SQLite database for memory storage."""
        self.db_connection = sqlite3.connect(str(self.db_path))
        cursor = self.db_connection.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS experiences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                importance REAL DEFAULT 0.5,
                tags TEXT,
                embedding BLOB
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                speaker TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                context TEXT,
                response_to INTEGER
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT,
                timestamp TEXT NOT NULL,
                success_rate REAL DEFAULT 0.0,
                retention_score REAL DEFAULT 0.0
            )
        ''')
        
        self.db_connection.commit()
    
    async def store_experience(self, experience: Dict[str, Any]):
        """Store a new experience in memory."""
        try:
            # Add to short-term memory
            experience["timestamp"] = datetime.now().isoformat()
            experience["id"] = len(self.short_term_memory)
            
            self.short_term_memory.append(experience)
            
            # Calculate importance
            importance = await self._calculate_importance(experience)
            experience["importance"] = importance
            
            # Store in database
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO experiences (type, content, timestamp, importance, tags)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                experience["type"],
                json.dumps(experience),
                experience["timestamp"],
                importance,
                json.dumps(experience.get("tags", []))
            ))
            self.db_connection.commit()
            
            # Manage memory limits
            await self._manage_memory_limits()
            
            self.logger.debug(f"Stored experience: {experience['type']}")
            
        except Exception as e:
            self.logger.error(f"Error storing experience: {e}")
    
    async def store_conversation(self, speaker: str, message: str, context: Optional[str] = None):
        """Store a conversation message."""
        try:
            conversation = {
                "speaker": speaker,
                "message": message,
                "timestamp": datetime.now().isoformat(),
                "context": context
            }
            
            # Store in database
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO conversations (speaker, message, timestamp, context)
                VALUES (?, ?, ?, ?)
            ''', (speaker, message, conversation["timestamp"], context))
            self.db_connection.commit()
            
            # Also store as experience
            await self.store_experience({
                "type": "conversation",
                "speaker": speaker,
                "message": message,
                "context": context
            })
            
        except Exception as e:
            self.logger.error(f"Error storing conversation: {e}")
    
    async def store_learning_event(self, topic: str, content: str, source: str, success_rate: float = 0.0):
        """Store a learning event."""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO learning_events (topic, content, source, timestamp, success_rate)
                VALUES (?, ?, ?, ?, ?)
            ''', (topic, content, source, datetime.now().isoformat(), success_rate))
            self.db_connection.commit()
            
            # Also store as experience
            await self.store_experience({
                "type": "learning",
                "topic": topic,
                "content": content,
                "source": source,
                "success_rate": success_rate
            })
            
        except Exception as e:
            self.logger.error(f"Error storing learning event: {e}")
    
    async def get_relevant_context(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Retrieve relevant context for a query."""
        try:
            # Search recent conversations
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT speaker, message, timestamp, context
                FROM conversations
                WHERE message LIKE ? OR context LIKE ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (f"%{query}%", f"%{query}%", limit))
            
            conversations = []
            for row in cursor.fetchall():
                conversations.append({
                    "type": "conversation",
                    "speaker": row[0],
                    "message": row[1],
                    "timestamp": row[2],
                    "context": row[3]
                })
            
            # Search relevant experiences
            cursor.execute('''
                SELECT type, content, timestamp, importance
                FROM experiences
                WHERE content LIKE ?
                ORDER BY importance DESC, timestamp DESC
                LIMIT ?
            ''', (f"%{query}%", limit))
            
            experiences = []
            for row in cursor.fetchall():
                experiences.append({
                    "type": row[0],
                    "content": json.loads(row[1]),
                    "timestamp": row[2],
                    "importance": row[3]
                })
            
            # Combine and sort by relevance
            context = conversations + experiences
            context.sort(key=lambda x: x.get("importance", 0.5), reverse=True)
            
            return context[:limit]
            
        except Exception as e:
            self.logger.error(f"Error retrieving context: {e}")
            return []
    
    async def get_recent_memories(self, hours: int = 24, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent memories from the specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_str = cutoff_time.isoformat()
            
            cursor = self.db_connection.cursor()
            cursor.execute('''
                SELECT type, content, timestamp, importance
                FROM experiences
                WHERE timestamp > ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (cutoff_str, limit))
            
            memories = []
            for row in cursor.fetchall():
                memories.append({
                    "type": row[0],
                    "content": json.loads(row[1]),
                    "timestamp": row[2],
                    "importance": row[3]
                })
            
            return memories
            
        except Exception as e:
            self.logger.error(f"Error retrieving recent memories: {e}")
            return []
    
    async def get_learning_history(self, topic: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get learning history, optionally filtered by topic."""
        try:
            cursor = self.db_connection.cursor()
            
            if topic:
                cursor.execute('''
                    SELECT topic, content, source, timestamp, success_rate, retention_score
                    FROM learning_events
                    WHERE topic LIKE ?
                    ORDER BY timestamp DESC
                ''', (f"%{topic}%",))
            else:
                cursor.execute('''
                    SELECT topic, content, source, timestamp, success_rate, retention_score
                    FROM learning_events
                    ORDER BY timestamp DESC
                ''')
            
            history = []
            for row in cursor.fetchall():
                history.append({
                    "topic": row[0],
                    "content": row[1],
                    "source": row[2],
                    "timestamp": row[3],
                    "success_rate": row[4],
                    "retention_score": row[5]
                })
            
            return history
            
        except Exception as e:
            self.logger.error(f"Error retrieving learning history: {e}")
            return []
    
    async def _calculate_importance(self, experience: Dict[str, Any]) -> float:
        """Calculate the importance score of an experience."""
        importance = 0.5  # Base importance
        
        # Increase importance for learning events
        if experience["type"] == "learning":
            importance += 0.3
        
        # Increase importance for task completion
        if experience["type"] == "task_completion":
            importance += 0.2
        
        # Increase importance for errors (learning opportunities)
        if "error" in experience.get("content", "").lower():
            importance += 0.2
        
        # Increase importance for new knowledge
        if experience["type"] == "knowledge_acquisition":
            importance += 0.4
        
        return min(importance, 1.0)
    
    async def _manage_memory_limits(self):
        """Manage memory limits by moving old memories to long-term storage."""
        # Move old short-term memories to long-term
        if len(self.short_term_memory) > self.short_term_limit:
            # Sort by importance and keep the most important
            self.short_term_memory.sort(key=lambda x: x.get("importance", 0.5), reverse=True)
            
            # Move excess to long-term
            excess = self.short_term_memory[self.short_term_limit:]
            self.long_term_memory.extend(excess)
            self.short_term_memory = self.short_term_memory[:self.short_term_limit]
        
        # Manage long-term memory limits
        if len(self.long_term_memory) > self.long_term_limit:
            # Keep only the most important memories
            self.long_term_memory.sort(key=lambda x: x.get("importance", 0.5), reverse=True)
            self.long_term_memory = self.long_term_memory[:self.long_term_limit]
    
    async def _load_memories(self):
        """Load existing memories from database."""
        try:
            # Load recent experiences into short-term memory
            recent_memories = await self.get_recent_memories(hours=1, limit=self.short_term_limit)
            self.short_term_memory = recent_memories
            
            self.logger.info(f"Loaded {len(recent_memories)} recent memories")
            
        except Exception as e:
            self.logger.error(f"Error loading memories: {e}")
    
    async def consolidate_memories(self):
        """Consolidate and organize memories for better retrieval."""
        try:
            # This would implement memory consolidation algorithms
            # For now, just manage limits
            await self._manage_memory_limits()
            
        except Exception as e:
            self.logger.error(f"Error consolidating memories: {e}")
    
    def __del__(self):
        """Cleanup database connection."""
        if self.db_connection:
            self.db_connection.close()
