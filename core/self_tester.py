"""
Self-Testing and Validation System for Sentient AI
Continuously tests knowledge and identifies areas for improvement.
"""

import asyncio
import logging
import json
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

class SelfTester:
    """
    Autonomous self-testing system that validates learning and identifies knowledge gaps.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Testing state
        self.test_history = []
        self.knowledge_gaps = []
        self.mastery_levels = {}
        self.test_schedule = {}
        
        # Test types
        self.test_types = {
            "recall": "Test ability to recall learned information",
            "comprehension": "Test understanding of concepts",
            "application": "Test ability to apply knowledge",
            "analysis": "Test analytical thinking",
            "synthesis": "Test ability to combine knowledge",
            "evaluation": "Test critical thinking and judgment"
        }
        
        # Test difficulty levels
        self.difficulty_levels = {
            "basic": 0.2,
            "intermediate": 0.5,
            "advanced": 0.8,
            "expert": 1.0
        }
    
    async def initialize(self):
        """Initialize the self-testing system."""
        self.logger.info("🧪 Initializing self-testing system...")
        
        # Load test history
        await self._load_test_history()
        
        # Schedule initial tests
        await self._schedule_initial_tests()
        
        self.logger.info("✅ Self-testing system initialized")
    
    async def identify_knowledge_gaps(self) -> List[str]:
        """Identify gaps in current knowledge."""
        try:
            gaps = []
            
            # Analyze recent test results
            recent_tests = await self._get_recent_tests(days=7)
            
            # Find topics with low performance
            topic_performance = {}
            for test in recent_tests:
                topic = test.get("topic", "unknown")
                score = test.get("score", 0.0)
                
                if topic not in topic_performance:
                    topic_performance[topic] = []
                topic_performance[topic].append(score)
            
            # Identify topics with consistently low scores
            for topic, scores in topic_performance.items():
                avg_score = sum(scores) / len(scores)
                if avg_score < 0.6:  # Below 60% threshold
                    gaps.append(f"Weak understanding of {topic}")
            
            # Identify completely untested areas
            fundamental_areas = [
                "logical reasoning",
                "mathematical concepts",
                "scientific principles",
                "language understanding",
                "problem solving strategies",
                "creative thinking",
                "ethical reasoning",
                "learning methodologies"
            ]
            
            tested_areas = set(topic_performance.keys())
            for area in fundamental_areas:
                if area not in tested_areas:
                    gaps.append(f"No knowledge tested in {area}")
            
            # Add exploration gaps
            exploration_gaps = [
                "emerging technologies",
                "interdisciplinary connections",
                "advanced problem solving",
                "meta-cognitive strategies"
            ]
            
            gaps.extend(exploration_gaps[:2])  # Add 2 exploration gaps
            
            self.knowledge_gaps = gaps
            self.logger.info(f"🔍 Identified {len(gaps)} knowledge gaps")
            
            return gaps
            
        except Exception as e:
            self.logger.error(f"Error identifying knowledge gaps: {e}")
            return ["Error in gap analysis - need to improve self-testing"]
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run a comprehensive test across all knowledge areas."""
        try:
            self.logger.info("🧪 Running comprehensive knowledge test...")
            
            test_results = {
                "test_id": f"comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "start_time": datetime.now().isoformat(),
                "test_type": "comprehensive",
                "results": {},
                "overall_score": 0.0,
                "areas_tested": 0,
                "gaps_identified": []
            }
            
            # Test different knowledge areas
            test_areas = [
                "factual_knowledge",
                "conceptual_understanding", 
                "procedural_knowledge",
                "problem_solving",
                "learning_ability",
                "self_awareness"
            ]
            
            total_score = 0.0
            areas_tested = 0
            
            for area in test_areas:
                area_result = await self._test_knowledge_area(area)
                test_results["results"][area] = area_result
                total_score += area_result["score"]
                areas_tested += 1
                
                # Identify gaps in this area
                if area_result["score"] < 0.6:
                    test_results["gaps_identified"].append(f"Weakness in {area}")
            
            # Calculate overall score
            test_results["overall_score"] = total_score / areas_tested if areas_tested > 0 else 0.0
            test_results["areas_tested"] = areas_tested
            test_results["end_time"] = datetime.now().isoformat()
            
            # Store test results
            self.test_history.append(test_results)
            await self._save_test_history()
            
            # Update mastery levels
            await self._update_mastery_levels(test_results)
            
            self.logger.info(f"✅ Comprehensive test completed. Overall score: {test_results['overall_score']:.2f}")
            
            return test_results
            
        except Exception as e:
            self.logger.error(f"Error running comprehensive test: {e}")
            return {"error": str(e), "overall_score": 0.0}
    
    async def test_topic_understanding(self, topic: str) -> Dict[str, Any]:
        """Test understanding of a specific topic."""
        try:
            self.logger.info(f"🧪 Testing understanding of: {topic}")
            
            test_result = {
                "topic": topic,
                "test_id": f"topic_{topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "timestamp": datetime.now().isoformat(),
                "test_type": "topic_specific",
                "passed": False,
                "score": 0.0,
                "weaknesses": [],
                "strengths": [],
                "recommendations": []
            }
            
            # Generate test questions for the topic
            questions = await self._generate_topic_questions(topic)
            
            # Simulate answering questions (in real implementation, this would test actual knowledge)
            correct_answers = 0
            total_questions = len(questions)
            weaknesses = []
            strengths = []
            
            for question in questions:
                # Simulate knowledge test
                answer_quality = await self._simulate_answer_quality(topic, question)
                
                if answer_quality > 0.6:
                    correct_answers += 1
                    strengths.append(question["area"])
                else:
                    weaknesses.append(question["area"])
            
            # Calculate score
            score = correct_answers / total_questions if total_questions > 0 else 0.0
            test_result["score"] = score
            test_result["passed"] = score >= 0.7  # 70% threshold
            test_result["weaknesses"] = list(set(weaknesses))
            test_result["strengths"] = list(set(strengths))
            
            # Generate recommendations
            if not test_result["passed"]:
                test_result["recommendations"] = [
                    f"Review fundamental concepts of {topic}",
                    f"Find additional learning resources for {topic}",
                    f"Practice applying {topic} knowledge",
                    "Focus on identified weak areas"
                ]
            
            # Store test result
            self.test_history.append(test_result)
            await self._save_test_history()
            
            self.logger.info(f"📊 Topic test completed. Score: {score:.2f}, Passed: {test_result['passed']}")
            
            return test_result
            
        except Exception as e:
            self.logger.error(f"Error testing topic understanding: {e}")
            return {
                "topic": topic,
                "passed": False,
                "score": 0.0,
                "error": str(e),
                "weaknesses": ["Error in testing system"],
                "recommendations": ["Fix testing system errors"]
            }
    
    async def _test_knowledge_area(self, area: str) -> Dict[str, Any]:
        """Test a specific knowledge area."""
        # Simulate testing different knowledge areas
        test_scenarios = {
            "factual_knowledge": [
                "Recall specific facts and information",
                "Remember learned concepts",
                "Access stored knowledge"
            ],
            "conceptual_understanding": [
                "Explain abstract concepts",
                "Show relationships between ideas",
                "Demonstrate deep understanding"
            ],
            "procedural_knowledge": [
                "Execute learned procedures",
                "Apply step-by-step processes",
                "Demonstrate practical skills"
            ],
            "problem_solving": [
                "Solve novel problems",
                "Apply knowledge creatively",
                "Find innovative solutions"
            ],
            "learning_ability": [
                "Learn new information quickly",
                "Adapt to new situations",
                "Improve from experience"
            ],
            "self_awareness": [
                "Understand own capabilities",
                "Recognize limitations",
                "Monitor learning progress"
            ]
        }
        
        scenarios = test_scenarios.get(area, ["General knowledge test"])
        
        # Simulate test performance
        performance_scores = []
        for scenario in scenarios:
            # Simulate varying performance
            base_score = random.uniform(0.4, 0.9)
            performance_scores.append(base_score)
        
        avg_score = sum(performance_scores) / len(performance_scores)
        
        return {
            "area": area,
            "score": avg_score,
            "scenarios_tested": len(scenarios),
            "individual_scores": performance_scores,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _generate_topic_questions(self, topic: str) -> List[Dict[str, Any]]:
        """Generate test questions for a topic."""
        # Generate different types of questions
        question_templates = [
            {"type": "recall", "area": "basic_facts", "template": f"What are the key facts about {topic}?"},
            {"type": "comprehension", "area": "understanding", "template": f"Explain how {topic} works"},
            {"type": "application", "area": "practical_use", "template": f"How would you apply {topic} in practice?"},
            {"type": "analysis", "area": "critical_thinking", "template": f"What are the strengths and weaknesses of {topic}?"},
            {"type": "synthesis", "area": "connections", "template": f"How does {topic} relate to other concepts?"},
            {"type": "evaluation", "area": "judgment", "template": f"What is the value and importance of {topic}?"}
        ]
        
        return question_templates
    
    async def _simulate_answer_quality(self, topic: str, question: Dict[str, Any]) -> float:
        """Simulate the quality of an answer to a test question."""
        # This simulates knowledge testing - in real implementation, 
        # this would actually test the AI's knowledge
        
        # Base quality varies by question type
        base_quality = {
            "recall": 0.7,
            "comprehension": 0.6,
            "application": 0.5,
            "analysis": 0.4,
            "synthesis": 0.3,
            "evaluation": 0.4
        }
        
        question_type = question.get("type", "recall")
        quality = base_quality.get(question_type, 0.5)
        
        # Add some randomness
        quality += random.uniform(-0.2, 0.2)
        
        return max(0.0, min(1.0, quality))
    
    async def _get_recent_tests(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get test results from recent days."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        recent_tests = []
        for test in self.test_history:
            test_date = datetime.fromisoformat(test.get("timestamp", test.get("start_time", "1970-01-01")))
            if test_date > cutoff_date:
                recent_tests.append(test)
        
        return recent_tests
    
    async def _update_mastery_levels(self, test_results: Dict[str, Any]):
        """Update mastery levels based on test results."""
        overall_score = test_results.get("overall_score", 0.0)
        
        # Update overall mastery
        if "overall" not in self.mastery_levels:
            self.mastery_levels["overall"] = 0.0
        
        # Weighted average with previous mastery
        current_mastery = self.mastery_levels["overall"]
        self.mastery_levels["overall"] = (current_mastery * 0.7) + (overall_score * 0.3)
        
        # Update area-specific mastery
        for area, result in test_results.get("results", {}).items():
            if area not in self.mastery_levels:
                self.mastery_levels[area] = 0.0
            
            area_score = result.get("score", 0.0)
            current_area_mastery = self.mastery_levels[area]
            self.mastery_levels[area] = (current_area_mastery * 0.7) + (area_score * 0.3)
    
    async def _schedule_initial_tests(self):
        """Schedule initial testing sessions."""
        # Schedule tests for different time intervals
        now = datetime.now()
        
        self.test_schedule = {
            "daily_quick_test": now + timedelta(hours=1),
            "weekly_comprehensive": now + timedelta(days=1),
            "monthly_deep_dive": now + timedelta(days=7),
            "knowledge_gap_review": now + timedelta(hours=6)
        }
    
    async def _load_test_history(self):
        """Load test history from disk."""
        try:
            history_file = self.config.data_dir / "test_history.json"
            if history_file.exists():
                with open(history_file, 'r') as f:
                    data = json.load(f)
                    self.test_history = data.get("test_history", [])
                    self.mastery_levels = data.get("mastery_levels", {})
                self.logger.info(f"Loaded {len(self.test_history)} test records")
        except Exception as e:
            self.logger.warning(f"Could not load test history: {e}")
            self.test_history = []
            self.mastery_levels = {}
    
    async def _save_test_history(self):
        """Save test history to disk."""
        try:
            history_file = self.config.data_dir / "test_history.json"
            data = {
                "test_history": self.test_history,
                "mastery_levels": self.mastery_levels,
                "last_updated": datetime.now().isoformat()
            }
            with open(history_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save test history: {e}")
    
    def get_mastery_summary(self) -> Dict[str, Any]:
        """Get a summary of current mastery levels."""
        return {
            "overall_mastery": self.mastery_levels.get("overall", 0.0),
            "area_mastery": self.mastery_levels,
            "total_tests": len(self.test_history),
            "recent_gaps": self.knowledge_gaps[-5:] if self.knowledge_gaps else [],
            "last_test": self.test_history[-1]["timestamp"] if self.test_history else None
        }
