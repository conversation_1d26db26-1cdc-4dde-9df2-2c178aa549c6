"""
Core Sentient AI - The main autonomous learning system
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from .memory_system import MemorySystem
from .learning_engine import LearningEngine
from .knowledge_manager import KnowledgeManager
from .task_executor import TaskExecutor
from .self_tester import SelfTester
from .web_learner import Web<PERSON>earner
from .config import Config

class SentientAI:
    """
    The core Sentient AI system that autonomously learns and improves itself.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Core systems
        self.memory = MemorySystem(config)
        self.knowledge = KnowledgeManager(config)
        self.learning_engine = LearningEngine(config)
        self.task_executor = TaskExecutor(config)
        self.self_tester = SelfTester(config)
        self.web_learner = WebLearner(config)
        
        # State
        self.is_running = False
        self.learning_active = False
        self.current_task = None
        self.learning_history = []
        
        # Autonomous learning loop
        self.learning_loop_task = None
        
    async def initialize(self):
        """Initialize the AI and start autonomous learning."""
        self.logger.info("🧠 Initializing Sentient AI core systems...")
        
        # Initialize all subsystems
        await self.memory.initialize()
        await self.knowledge.initialize()
        await self.learning_engine.initialize()
        await self.task_executor.initialize()
        await self.self_tester.initialize()
        await self.web_learner.initialize()
        
        # Load existing knowledge and memories
        await self._load_state()
        
        # Start autonomous learning if configured
        if self.config.learning_config["auto_start_learning"]:
            await self.start_autonomous_learning()
        
        self.is_running = True
        self.logger.info("✅ Sentient AI initialized and ready")
    
    async def start_autonomous_learning(self):
        """Start the autonomous learning process."""
        if self.learning_active:
            return
        
        self.learning_active = True
        self.logger.info("🌐 Starting autonomous learning from internet resources...")
        
        # Start the continuous learning loop
        self.learning_loop_task = asyncio.create_task(self._autonomous_learning_loop())
        
        # Begin initial learning phase
        await self._begin_initial_learning()
    
    async def _autonomous_learning_loop(self):
        """Main autonomous learning loop that runs continuously."""
        while self.learning_active:
            try:
                # Self-assess current knowledge
                knowledge_gaps = await self.self_tester.identify_knowledge_gaps()
                
                # Generate learning goals
                learning_goals = await self.learning_engine.generate_learning_goals(knowledge_gaps)
                
                # Execute learning for each goal
                for goal in learning_goals:
                    if not self.learning_active:
                        break
                    
                    await self._learn_topic(goal)
                
                # Self-test and validate learning
                await self.self_tester.run_comprehensive_test()
                
                # Update knowledge base
                await self.knowledge.consolidate_learning()
                
                # Wait before next learning cycle
                await asyncio.sleep(self.config.ai_config["knowledge_update_interval"])
                
            except Exception as e:
                self.logger.error(f"Error in learning loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _begin_initial_learning(self):
        """Begin the initial learning phase where AI teaches itself basics."""
        self.logger.info("📚 Beginning initial self-teaching phase...")
        
        # Core topics the AI should learn first
        initial_topics = [
            "how to learn effectively",
            "critical thinking and reasoning",
            "problem solving methodologies",
            "research techniques",
            "programming fundamentals",
            "mathematics and logic",
            "science and technology",
            "communication and language",
            "self-improvement strategies",
            "goal setting and planning"
        ]
        
        for topic in initial_topics:
            if not self.learning_active:
                break
            
            self.logger.info(f"🎯 Learning: {topic}")
            await self._learn_topic(topic)
    
    async def _learn_topic(self, topic: str):
        """Learn a specific topic from web resources."""
        try:
            # Search for learning resources
            resources = await self.web_learner.find_learning_resources(topic)
            
            # Process and learn from each resource
            for resource in resources:
                knowledge = await self.web_learner.extract_knowledge(resource)
                await self.knowledge.add_knowledge(topic, knowledge)
                
                # Store learning experience
                await self.memory.store_experience({
                    "type": "learning",
                    "topic": topic,
                    "resource": resource,
                    "knowledge_gained": knowledge,
                    "timestamp": datetime.now().isoformat()
                })
            
            # Test understanding of the topic
            test_results = await self.self_tester.test_topic_understanding(topic)
            
            # If test failed, relearn with different approach
            if not test_results["passed"]:
                self.logger.info(f"🔄 Relearning {topic} - test failed")
                await self._relearn_topic(topic, test_results["weaknesses"])
            
        except Exception as e:
            self.logger.error(f"Error learning topic {topic}: {e}")
    
    async def _relearn_topic(self, topic: str, weaknesses: List[str]):
        """Relearn a topic focusing on identified weaknesses."""
        # Find alternative learning resources
        alternative_resources = await self.web_learner.find_alternative_resources(topic, weaknesses)
        
        # Learn from alternative sources
        for resource in alternative_resources:
            knowledge = await self.web_learner.extract_knowledge(resource)
            await self.knowledge.update_knowledge(topic, knowledge)
    
    async def chat(self, message: str) -> str:
        """Process a chat message and return response."""
        try:
            # Store the conversation in memory
            await self.memory.store_conversation("user", message)
            
            # Analyze the message for tasks or questions
            analysis = await self.task_executor.analyze_message(message)
            
            if analysis["is_task"]:
                # Handle task execution
                response = await self._handle_task(analysis["task"])
            else:
                # Handle general conversation
                response = await self._generate_response(message)
            
            # Store AI response
            await self.memory.store_conversation("ai", response)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing chat message: {e}")
            return f"I encountered an error: {e}. Let me learn how to handle this better."
    
    async def _handle_task(self, task: Dict[str, Any]) -> str:
        """Handle task execution."""
        task_name = task["name"]
        task_description = task["description"]
        
        self.logger.info(f"🎯 Received task: {task_name}")
        
        # Check if AI knows how to do this task
        can_execute = await self.task_executor.can_execute_task(task)
        
        if not can_execute:
            # Learn how to do the task
            self.logger.info(f"📚 Learning how to: {task_name}")
            await self._learn_task_execution(task)
        
        # Execute the task
        result = await self.task_executor.execute_task(task)
        
        return f"Task '{task_name}' completed. Result: {result}"
    
    async def _learn_task_execution(self, task: Dict[str, Any]):
        """Learn how to execute a specific task."""
        task_name = task["name"]
        
        # Search for tutorials and guides
        learning_resources = await self.web_learner.find_task_tutorials(task_name)
        
        # Learn from each resource
        for resource in learning_resources:
            instructions = await self.web_learner.extract_task_instructions(resource)
            await self.knowledge.add_task_knowledge(task_name, instructions)
        
        # Practice the task if possible
        await self.task_executor.practice_task(task)
    
    async def _generate_response(self, message: str) -> str:
        """Generate a conversational response."""
        # Retrieve relevant memories and knowledge
        context = await self.memory.get_relevant_context(message)
        knowledge = await self.knowledge.get_relevant_knowledge(message)
        
        # Generate response using learning engine
        response = await self.learning_engine.generate_response(message, context, knowledge)
        
        return response
    
    async def _load_state(self):
        """Load existing state from disk."""
        try:
            state_file = self.config.data_dir / "ai_state.json"
            if state_file.exists():
                with open(state_file, 'r') as f:
                    state = json.load(f)
                    self.learning_history = state.get("learning_history", [])
                    self.logger.info("📁 Loaded existing AI state")
        except Exception as e:
            self.logger.warning(f"Could not load state: {e}")
    
    async def save_state(self):
        """Save current state to disk."""
        try:
            state = {
                "learning_history": self.learning_history,
                "timestamp": datetime.now().isoformat()
            }
            
            state_file = self.config.data_dir / "ai_state.json"
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Could not save state: {e}")
    
    async def shutdown(self):
        """Shutdown the AI system."""
        self.logger.info("🛑 Shutting down Sentient AI...")
        
        self.learning_active = False
        self.is_running = False
        
        if self.learning_loop_task:
            self.learning_loop_task.cancel()
        
        # Save current state
        await self.save_state()
        
        self.logger.info("✅ Sentient AI shutdown complete")
