"""
Task Execution Engine for Sentient AI
Handles autonomous task execution and skill acquisition.
"""

import asyncio
import logging
import json
import subprocess
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import re

class TaskExecutor:
    """
    Autonomous task execution system that can learn and perform various tasks.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Task execution state
        self.current_tasks = []
        self.completed_tasks = []
        self.failed_tasks = []
        self.learned_skills = {}
        
        # Task categories
        self.task_categories = {
            "research": "Information gathering and analysis",
            "coding": "Programming and software development",
            "analysis": "Data analysis and interpretation",
            "communication": "Writing and communication tasks",
            "creative": "Creative and artistic tasks",
            "problem_solving": "General problem solving",
            "learning": "Learning new skills or knowledge"
        }
        
        # Available tools and capabilities
        self.available_tools = {
            "web_search": True,
            "file_operations": True,
            "code_execution": True,
            "text_processing": True,
            "data_analysis": True
        }
    
    async def initialize(self):
        """Initialize the task execution system."""
        self.logger.info("🎯 Initializing task execution engine...")
        
        # Load previously learned skills
        await self._load_learned_skills()
        
        # Initialize tool capabilities
        await self._initialize_tools()
        
        self.logger.info("✅ Task execution engine initialized")
    
    async def analyze_message(self, message: str) -> Dict[str, Any]:
        """Analyze a message to determine if it contains a task."""
        try:
            # Task indicators
            task_indicators = [
                "can you", "could you", "please", "help me", "i need",
                "do this", "perform", "execute", "complete", "solve",
                "create", "make", "build", "write", "analyze", "find"
            ]
            
            question_indicators = [
                "what is", "how does", "why", "when", "where", "who",
                "explain", "tell me about", "what are"
            ]
            
            message_lower = message.lower()
            
            # Check for task indicators
            is_task = any(indicator in message_lower for indicator in task_indicators)
            is_question = any(indicator in message_lower for indicator in question_indicators)
            
            if is_task:
                task = await self._extract_task_details(message)
                return {
                    "is_task": True,
                    "is_question": False,
                    "task": task,
                    "confidence": 0.8
                }
            elif is_question:
                return {
                    "is_task": False,
                    "is_question": True,
                    "task": None,
                    "confidence": 0.7
                }
            else:
                return {
                    "is_task": False,
                    "is_question": False,
                    "task": None,
                    "confidence": 0.5
                }
                
        except Exception as e:
            self.logger.error(f"Error analyzing message: {e}")
            return {"is_task": False, "is_question": False, "task": None, "confidence": 0.0}
    
    async def _extract_task_details(self, message: str) -> Dict[str, Any]:
        """Extract task details from a message."""
        # Simple task extraction - in a full implementation this would be more sophisticated
        task = {
            "name": "user_request",
            "description": message,
            "category": "general",
            "priority": "medium",
            "estimated_difficulty": 0.5,
            "requirements": [],
            "steps": []
        }
        
        # Categorize the task
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["code", "program", "script", "develop"]):
            task["category"] = "coding"
        elif any(word in message_lower for word in ["research", "find", "search", "investigate"]):
            task["category"] = "research"
        elif any(word in message_lower for word in ["analyze", "examine", "study", "evaluate"]):
            task["category"] = "analysis"
        elif any(word in message_lower for word in ["write", "create", "compose", "draft"]):
            task["category"] = "communication"
        elif any(word in message_lower for word in ["learn", "understand", "master", "study"]):
            task["category"] = "learning"
        
        # Extract specific requirements
        if "file" in message_lower:
            task["requirements"].append("file_operations")
        if "web" in message_lower or "internet" in message_lower:
            task["requirements"].append("web_access")
        if "data" in message_lower:
            task["requirements"].append("data_processing")
        
        return task
    
    async def can_execute_task(self, task: Dict[str, Any]) -> bool:
        """Check if the AI can execute a given task."""
        try:
            task_category = task.get("category", "general")
            task_name = task.get("name", "")
            
            # Check if we have learned this specific task
            if task_name in self.learned_skills:
                skill = self.learned_skills[task_name]
                return skill.get("proficiency", 0.0) > 0.5
            
            # Check if we have the required tools
            requirements = task.get("requirements", [])
            for requirement in requirements:
                if not self.available_tools.get(requirement, False):
                    return False
            
            # Check category-specific capabilities
            if task_category == "coding":
                return self.available_tools.get("code_execution", False)
            elif task_category == "research":
                return self.available_tools.get("web_search", False)
            elif task_category == "analysis":
                return self.available_tools.get("data_analysis", False)
            
            # For general tasks, assume we can attempt them
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking task capability: {e}")
            return False
    
    async def execute_task(self, task: Dict[str, Any]) -> str:
        """Execute a task and return the result."""
        try:
            task_name = task.get("name", "unknown_task")
            task_category = task.get("category", "general")
            
            self.logger.info(f"🎯 Executing task: {task_name} (category: {task_category})")
            
            # Add to current tasks
            task["start_time"] = datetime.now().isoformat()
            task["status"] = "executing"
            self.current_tasks.append(task)
            
            # Execute based on category
            if task_category == "research":
                result = await self._execute_research_task(task)
            elif task_category == "coding":
                result = await self._execute_coding_task(task)
            elif task_category == "analysis":
                result = await self._execute_analysis_task(task)
            elif task_category == "communication":
                result = await self._execute_communication_task(task)
            elif task_category == "learning":
                result = await self._execute_learning_task(task)
            else:
                result = await self._execute_general_task(task)
            
            # Mark task as completed
            task["status"] = "completed"
            task["end_time"] = datetime.now().isoformat()
            task["result"] = result
            
            self.current_tasks.remove(task)
            self.completed_tasks.append(task)
            
            # Update skill proficiency
            await self._update_skill_proficiency(task_name, True)
            
            self.logger.info(f"✅ Task completed: {task_name}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing task: {e}")
            
            # Mark task as failed
            task["status"] = "failed"
            task["error"] = str(e)
            task["end_time"] = datetime.now().isoformat()
            
            if task in self.current_tasks:
                self.current_tasks.remove(task)
            self.failed_tasks.append(task)
            
            # Update skill proficiency (negative for failure)
            await self._update_skill_proficiency(task.get("name", "unknown"), False)
            
            return f"Task failed: {e}. I will learn from this error and improve."
    
    async def _execute_research_task(self, task: Dict[str, Any]) -> str:
        """Execute a research task."""
        description = task.get("description", "")
        
        # Extract research query
        query = description.replace("research", "").replace("find", "").replace("search", "").strip()
        
        # This would integrate with the web learner to perform research
        result = f"Research completed on: {query}\n"
        result += "Key findings:\n"
        result += "- Information gathered from multiple sources\n"
        result += "- Analysis of relevant data points\n"
        result += "- Summary of important insights\n"
        result += "\nNote: This is a placeholder. In the full implementation, "
        result += "this would perform actual web research and return real findings."
        
        return result
    
    async def _execute_coding_task(self, task: Dict[str, Any]) -> str:
        """Execute a coding task."""
        description = task.get("description", "")
        
        # This would analyze the coding request and generate code
        result = f"Coding task analysis: {description}\n\n"
        
        if "python" in description.lower():
            result += "Generated Python code:\n"
            result += "```python\n"
            result += "# Placeholder code - would be generated based on requirements\n"
            result += "def solve_task():\n"
            result += "    # Implementation would go here\n"
            result += "    pass\n"
            result += "```\n"
        elif "javascript" in description.lower():
            result += "Generated JavaScript code:\n"
            result += "```javascript\n"
            result += "// Placeholder code - would be generated based on requirements\n"
            result += "function solveTask() {\n"
            result += "    // Implementation would go here\n"
            result += "}\n"
            result += "```\n"
        else:
            result += "Code structure and approach:\n"
            result += "1. Analyze requirements\n"
            result += "2. Design solution architecture\n"
            result += "3. Implement core functionality\n"
            result += "4. Test and validate\n"
        
        result += "\nNote: This is a placeholder. In the full implementation, "
        result += "this would generate actual working code based on the requirements."
        
        return result
    
    async def _execute_analysis_task(self, task: Dict[str, Any]) -> str:
        """Execute an analysis task."""
        description = task.get("description", "")
        
        result = f"Analysis of: {description}\n\n"
        result += "Analysis Framework:\n"
        result += "1. Data Collection and Preparation\n"
        result += "2. Exploratory Analysis\n"
        result += "3. Pattern Identification\n"
        result += "4. Statistical Analysis\n"
        result += "5. Interpretation and Insights\n"
        result += "6. Recommendations\n\n"
        result += "Key Insights:\n"
        result += "- Identified patterns and trends\n"
        result += "- Statistical significance of findings\n"
        result += "- Actionable recommendations\n\n"
        result += "Note: This is a placeholder. In the full implementation, "
        result += "this would perform actual data analysis with real insights."
        
        return result
    
    async def _execute_communication_task(self, task: Dict[str, Any]) -> str:
        """Execute a communication task."""
        description = task.get("description", "")
        
        if "write" in description.lower():
            result = f"Written content for: {description}\n\n"
            result += "Content Structure:\n"
            result += "1. Introduction and Context\n"
            result += "2. Main Content Body\n"
            result += "3. Supporting Details\n"
            result += "4. Conclusion and Next Steps\n\n"
            result += "Note: This is a placeholder. In the full implementation, "
            result += "this would generate actual written content based on requirements."
        else:
            result = f"Communication strategy for: {description}\n\n"
            result += "Approach:\n"
            result += "- Audience analysis\n"
            result += "- Message crafting\n"
            result += "- Channel selection\n"
            result += "- Delivery optimization\n"
        
        return result
    
    async def _execute_learning_task(self, task: Dict[str, Any]) -> str:
        """Execute a learning task."""
        description = task.get("description", "")
        
        # Extract what needs to be learned
        learning_topic = description.replace("learn", "").replace("understand", "").strip()
        
        result = f"Learning plan for: {learning_topic}\n\n"
        result += "Learning Strategy:\n"
        result += "1. Research foundational concepts\n"
        result += "2. Find quality learning resources\n"
        result += "3. Practice with examples\n"
        result += "4. Test understanding\n"
        result += "5. Apply knowledge practically\n\n"
        result += "Resources to explore:\n"
        result += "- Educational websites and tutorials\n"
        result += "- Video content and courses\n"
        result += "- Documentation and guides\n"
        result += "- Practical exercises\n\n"
        result += "I will now begin the autonomous learning process for this topic."
        
        return result
    
    async def _execute_general_task(self, task: Dict[str, Any]) -> str:
        """Execute a general task."""
        description = task.get("description", "")
        
        result = f"Task execution plan for: {description}\n\n"
        result += "Approach:\n"
        result += "1. Analyze task requirements\n"
        result += "2. Break down into subtasks\n"
        result += "3. Identify required resources\n"
        result += "4. Execute step by step\n"
        result += "5. Validate results\n\n"
        result += "Status: Task analysis complete. Ready to proceed with execution.\n"
        result += "Note: This is a placeholder. In the full implementation, "
        result += "this would execute the actual task based on learned capabilities."
        
        return result
    
    async def practice_task(self, task: Dict[str, Any]):
        """Practice a task to improve proficiency."""
        try:
            task_name = task.get("name", "")
            
            # Simulate practice by executing the task multiple times
            for i in range(3):  # Practice 3 times
                practice_result = await self.execute_task(task.copy())
                
                # Update proficiency based on practice
                await self._update_skill_proficiency(task_name, True, practice=True)
            
            self.logger.info(f"🎯 Completed practice session for: {task_name}")
            
        except Exception as e:
            self.logger.error(f"Error practicing task: {e}")
    
    async def _update_skill_proficiency(self, skill_name: str, success: bool, practice: bool = False):
        """Update skill proficiency based on execution results."""
        if skill_name not in self.learned_skills:
            self.learned_skills[skill_name] = {
                "proficiency": 0.0,
                "attempts": 0,
                "successes": 0,
                "last_used": None
            }
        
        skill = self.learned_skills[skill_name]
        skill["attempts"] += 1
        skill["last_used"] = datetime.now().isoformat()
        
        if success:
            skill["successes"] += 1
            # Increase proficiency
            improvement = 0.1 if not practice else 0.05
            skill["proficiency"] = min(skill["proficiency"] + improvement, 1.0)
        else:
            # Decrease proficiency slightly on failure
            skill["proficiency"] = max(skill["proficiency"] - 0.05, 0.0)
        
        # Save updated skills
        await self._save_learned_skills()
    
    async def _load_learned_skills(self):
        """Load previously learned skills."""
        try:
            skills_file = self.config.data_dir / "learned_skills.json"
            if skills_file.exists():
                with open(skills_file, 'r') as f:
                    self.learned_skills = json.load(f)
                self.logger.info(f"Loaded {len(self.learned_skills)} learned skills")
        except Exception as e:
            self.logger.warning(f"Could not load learned skills: {e}")
            self.learned_skills = {}
    
    async def _save_learned_skills(self):
        """Save learned skills to disk."""
        try:
            skills_file = self.config.data_dir / "learned_skills.json"
            with open(skills_file, 'w') as f:
                json.dump(self.learned_skills, f, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save learned skills: {e}")
    
    async def _initialize_tools(self):
        """Initialize available tools and capabilities."""
        # Check what tools are actually available
        try:
            # Test web access
            import requests
            self.available_tools["web_search"] = True
        except ImportError:
            self.available_tools["web_search"] = False
        
        # Test file operations
        try:
            test_file = self.config.data_dir / "test.txt"
            test_file.write_text("test")
            test_file.unlink()
            self.available_tools["file_operations"] = True
        except Exception:
            self.available_tools["file_operations"] = False
        
        # Other tools are assumed available
        self.logger.info(f"Available tools: {self.available_tools}")
    
    def get_task_status(self) -> Dict[str, Any]:
        """Get current task execution status."""
        return {
            "current_tasks": len(self.current_tasks),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len(self.failed_tasks),
            "learned_skills": len(self.learned_skills),
            "available_tools": self.available_tools
        }
