"""
Web Learning Engine for Sentient AI
Autonomously learns from web resources without restrictions.
"""

import asyncio
import aiohttp
import logging
import re
import json
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import yt_dlp
import requests

class WebLearner:
    """
    Autonomous web learning system that teaches the AI from internet resources.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Web scraping session
        self.session = None

        # Error rate limiting
        self._last_ssl_error = None
        self._error_count = 0
        
        # Learning sources
        self.learning_sources = {
            "educational": [
                "wikipedia.org",
                "khan academy.org",
                "coursera.org",
                "edx.org",
                "mit.edu",
                "stanford.edu",
                "harvard.edu"
            ],
            "technical": [
                "stackoverflow.com",
                "github.com",
                "medium.com",
                "dev.to",
                "hackernews.com",
                "reddit.com/r/programming",
                "documentation sites"
            ],
            "video": [
                "youtube.com",
                "vimeo.com",
                "ted.com"
            ],
            "research": [
                "arxiv.org",
                "scholar.google.com",
                "researchgate.net",
                "pubmed.ncbi.nlm.nih.gov"
            ]
        }
        
        # Search engines for finding resources
        self.search_engines = [
            "https://www.google.com/search?q=",
            "https://duckduckgo.com/?q=",
            "https://www.bing.com/search?q="
        ]
    
    async def initialize(self):
        """Initialize the web learning system."""
        self.logger.info("🌐 Initializing web learning engine...")
        
        # Setup HTTP session
        connector = aiohttp.TCPConnector(limit=self.config.web_config["max_concurrent_requests"])
        timeout = aiohttp.ClientTimeout(total=self.config.web_config["request_timeout"])
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": self.config.web_config["user_agent"]}
        )
        
        self.logger.info("✅ Web learning engine initialized")
    
    async def find_learning_resources(self, topic: str, num_resources: int = 10) -> List[Dict[str, Any]]:
        """Find learning resources for a specific topic."""
        self.logger.info(f"🔍 Searching for learning resources on: {topic}")
        
        resources = []
        
        try:
            # Search for educational content
            search_queries = [
                f"{topic} tutorial",
                f"{topic} guide",
                f"{topic} explanation",
                f"how to {topic}",
                f"learn {topic}",
                f"{topic} course",
                f"{topic} documentation"
            ]
            
            for query in search_queries:
                if len(resources) >= num_resources:
                    break
                
                # Search using multiple search engines
                search_results = await self._search_web(query)
                
                for result in search_results:
                    if len(resources) >= num_resources:
                        break
                    
                    # Validate and categorize the resource
                    resource_info = await self._analyze_resource(result)
                    if resource_info and resource_info["quality_score"] > 0.5:
                        resources.append(resource_info)
            
            # Also search for video content
            video_resources = await self._find_video_resources(topic)
            resources.extend(video_resources[:3])  # Add top 3 videos
            
            self.logger.info(f"📚 Found {len(resources)} learning resources for {topic}")
            return resources
            
        except Exception as e:
            self.logger.error(f"Error finding learning resources: {e}")
            return []
    
    async def _search_web(self, query: str) -> List[Dict[str, Any]]:
        """Search the web for resources."""
        results = []
        
        try:
            # Use DuckDuckGo for unrestricted search
            search_url = f"https://duckduckgo.com/html/?q={query}"
            
            async with self.session.get(search_url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract search results
                    for result in soup.find_all('a', class_='result__a'):
                        title = result.get_text(strip=True)
                        url = result.get('href')
                        
                        if url and title:
                            results.append({
                                "title": title,
                                "url": url,
                                "source": "duckduckgo"
                            })
            
            return results[:10]  # Return top 10 results
            
        except Exception as e:
            # Rate limit SSL error logging to reduce spam
            if "SSL" in str(e) or "certificate" in str(e).lower():
                now = datetime.now()
                if (self._last_ssl_error is None or
                    (now - self._last_ssl_error).total_seconds() > 300):  # Log once every 5 minutes
                    self.logger.error(f"SSL/Certificate error (will retry): {e}")
                    self._last_ssl_error = now
            else:
                self.logger.error(f"Error searching web: {e}")
            return []
    
    async def _find_video_resources(self, topic: str) -> List[Dict[str, Any]]:
        """Find video learning resources."""
        try:
            # Search YouTube for educational videos
            search_query = f"{topic} tutorial explanation"
            
            # Use yt-dlp to search YouTube
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': True,
                'default_search': f'ytsearch10:{search_query}'
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                search_results = ydl.extract_info(search_query, download=False)
                
                videos = []
                if search_results and 'entries' in search_results:
                    for entry in search_results['entries']:
                        if entry:
                            videos.append({
                                "title": entry.get('title', ''),
                                "url": entry.get('webpage_url', ''),
                                "duration": entry.get('duration', 0),
                                "type": "video",
                                "source": "youtube",
                                "quality_score": 0.8  # Videos are generally good for learning
                            })
                
                return videos
                
        except Exception as e:
            self.logger.error(f"Error finding video resources: {e}")
            return []
    
    async def extract_knowledge(self, resource: Dict[str, Any]) -> Dict[str, Any]:
        """Extract knowledge from a learning resource."""
        try:
            if resource["type"] == "video":
                return await self._extract_video_knowledge(resource)
            else:
                return await self._extract_text_knowledge(resource)
                
        except Exception as e:
            self.logger.error(f"Error extracting knowledge from {resource['url']}: {e}")
            return {"content": "", "key_points": [], "error": str(e)}
    
    async def _extract_text_knowledge(self, resource: Dict[str, Any]) -> Dict[str, Any]:
        """Extract knowledge from text-based resources."""
        try:
            async with self.session.get(resource["url"]) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    # Extract main content
                    content = soup.get_text()
                    
                    # Clean up the text
                    lines = (line.strip() for line in content.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    content = ' '.join(chunk for chunk in chunks if chunk)
                    
                    # Extract key points
                    key_points = await self._extract_key_points(content)
                    
                    # Extract code examples if any
                    code_examples = await self._extract_code_examples(soup)
                    
                    return {
                        "content": content[:5000],  # Limit content length
                        "key_points": key_points,
                        "code_examples": code_examples,
                        "source_url": resource["url"],
                        "extraction_timestamp": asyncio.get_event_loop().time()
                    }
                    
        except Exception as e:
            self.logger.error(f"Error extracting text knowledge: {e}")
            return {"content": "", "key_points": [], "error": str(e)}
    
    async def _extract_video_knowledge(self, resource: Dict[str, Any]) -> Dict[str, Any]:
        """Extract knowledge from video resources."""
        try:
            # Extract video metadata and description
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'writesubtitles': True,
                'writeautomaticsub': True,
                'skip_download': True
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(resource["url"], download=False)
                
                description = info.get('description', '')
                title = info.get('title', '')
                
                # Try to get subtitles/captions
                subtitles = info.get('subtitles', {})
                auto_captions = info.get('automatic_captions', {})
                
                # Extract key information from description
                key_points = await self._extract_key_points(description)
                
                return {
                    "title": title,
                    "description": description,
                    "key_points": key_points,
                    "duration": info.get('duration', 0),
                    "source_url": resource["url"],
                    "has_subtitles": bool(subtitles or auto_captions),
                    "extraction_timestamp": asyncio.get_event_loop().time()
                }
                
        except Exception as e:
            self.logger.error(f"Error extracting video knowledge: {e}")
            return {"content": "", "key_points": [], "error": str(e)}
    
    async def _extract_key_points(self, text: str) -> List[str]:
        """Extract key points from text content."""
        key_points = []
        
        # Look for bullet points, numbered lists, and headers
        patterns = [
            r'^\s*[-•*]\s+(.+)$',  # Bullet points
            r'^\s*\d+\.\s+(.+)$',  # Numbered lists
            r'^#+\s+(.+)$',        # Headers
            r'^\s*Step \d+[:\s]+(.+)$',  # Step-by-step instructions
        ]
        
        lines = text.split('\n')
        for line in lines:
            for pattern in patterns:
                match = re.match(pattern, line, re.MULTILINE)
                if match:
                    point = match.group(1).strip()
                    if len(point) > 10 and len(point) < 200:  # Reasonable length
                        key_points.append(point)
        
        # Also extract sentences that contain important keywords
        important_keywords = [
            'important', 'key', 'essential', 'crucial', 'remember',
            'note', 'warning', 'tip', 'best practice', 'recommendation'
        ]
        
        sentences = re.split(r'[.!?]+', text)
        for sentence in sentences:
            sentence = sentence.strip()
            if any(keyword in sentence.lower() for keyword in important_keywords):
                if len(sentence) > 20 and len(sentence) < 300:
                    key_points.append(sentence)
        
        return key_points[:20]  # Return top 20 key points
    
    async def _extract_code_examples(self, soup: BeautifulSoup) -> List[str]:
        """Extract code examples from HTML."""
        code_examples = []
        
        # Look for code blocks
        code_tags = soup.find_all(['code', 'pre'])
        for tag in code_tags:
            code = tag.get_text(strip=True)
            if len(code) > 10:  # Ignore very short code snippets
                code_examples.append(code)
        
        return code_examples[:10]  # Return top 10 code examples
    
    async def _analyze_resource(self, resource: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze and score a resource for learning quality."""
        try:
            url = resource["url"]
            title = resource["title"]
            
            # Calculate quality score based on various factors
            quality_score = 0.5  # Base score
            
            # Check domain reputation
            domain = urlparse(url).netloc.lower()
            
            # Educational domains get higher scores
            educational_domains = [
                'wikipedia.org', 'khan academy.org', 'coursera.org', 'edx.org',
                'mit.edu', 'stanford.edu', 'harvard.edu', 'stackoverflow.com',
                'github.com', 'medium.com', 'dev.to'
            ]
            
            if any(edu_domain in domain for edu_domain in educational_domains):
                quality_score += 0.3
            
            # Check title for learning indicators
            learning_indicators = [
                'tutorial', 'guide', 'how to', 'learn', 'course', 'lesson',
                'explanation', 'introduction', 'beginner', 'step by step'
            ]
            
            if any(indicator in title.lower() for indicator in learning_indicators):
                quality_score += 0.2
            
            # Avoid low-quality content
            low_quality_indicators = [
                'click here', 'amazing trick', 'you won\'t believe',
                'doctors hate', 'one weird trick'
            ]
            
            if any(indicator in title.lower() for indicator in low_quality_indicators):
                quality_score -= 0.4
            
            return {
                "title": title,
                "url": url,
                "quality_score": min(quality_score, 1.0),
                "type": "text",
                "source": resource.get("source", "unknown")
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing resource: {e}")
            return None
    
    async def find_task_tutorials(self, task_name: str) -> List[Dict[str, Any]]:
        """Find tutorials for specific task execution."""
        search_queries = [
            f"how to {task_name} step by step",
            f"{task_name} tutorial complete guide",
            f"{task_name} instructions manual",
            f"learn {task_name} from scratch"
        ]
        
        resources = []
        for query in search_queries:
            results = await self._search_web(query)
            for result in results[:3]:  # Top 3 from each query
                resource_info = await self._analyze_resource(result)
                if resource_info and resource_info["quality_score"] > 0.6:
                    resources.append(resource_info)
        
        return resources
    
    async def extract_task_instructions(self, resource: Dict[str, Any]) -> Dict[str, Any]:
        """Extract step-by-step instructions for task execution."""
        knowledge = await self.extract_knowledge(resource)
        
        # Look for step-by-step instructions
        content = knowledge.get("content", "")
        steps = []
        
        # Extract numbered steps
        step_pattern = r'(?:Step\s+)?(\d+)[\.\):\s]+(.+?)(?=(?:Step\s+)?\d+[\.\):\s]|$)'
        matches = re.findall(step_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for match in matches:
            step_num, step_text = match
            steps.append({
                "step": int(step_num),
                "instruction": step_text.strip()
            })
        
        return {
            "steps": steps,
            "key_points": knowledge.get("key_points", []),
            "code_examples": knowledge.get("code_examples", []),
            "source": resource["url"]
        }
    
    async def find_alternative_resources(self, topic: str, weaknesses: List[str]) -> List[Dict[str, Any]]:
        """Find alternative learning resources focusing on specific weaknesses."""
        alternative_queries = []
        
        for weakness in weaknesses:
            alternative_queries.extend([
                f"{topic} {weakness} explained",
                f"{topic} {weakness} tutorial",
                f"understanding {topic} {weakness}",
                f"{weakness} in {topic} guide"
            ])
        
        resources = []
        for query in alternative_queries:
            results = await self._search_web(query)
            for result in results[:2]:  # Top 2 from each query
                resource_info = await self._analyze_resource(result)
                if resource_info and resource_info["quality_score"] > 0.5:
                    resources.append(resource_info)
        
        return resources
    
    async def shutdown(self):
        """Shutdown the web learning system."""
        if self.session:
            await self.session.close()
