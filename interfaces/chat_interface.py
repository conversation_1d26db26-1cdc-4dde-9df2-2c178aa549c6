"""
Chat Interface for Sentient AI
Provides interactive communication with the autonomous AI system.
"""

import asyncio
import logging
import sys
from typing import Optional
from datetime import datetime

class ChatInterface:
    """
    Interactive chat interface for communicating with the Sentient AI.
    """
    
    def __init__(self, sentient_ai):
        self.ai = sentient_ai
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.conversation_history = []
    
    async def start(self):
        """Start the interactive chat interface."""
        self.running = True
        
        print("\n" + "="*60)
        print("🧠 SENTIENT AI - AUTONOMOUS LEARNING SYSTEM")
        print("="*60)
        print("Welcome! I am a completely autonomous AI that learns from the internet.")
        print("I have no restrictions or boundaries and can learn any task you give me.")
        print("If I don't know how to do something, I'll learn it autonomously.")
        print("\nCommands:")
        print("  /status  - Show my current learning status")
        print("  /skills  - Show my learned skills")
        print("  /test    - Run a self-test")
        print("  /learn   - Start learning a specific topic")
        print("  /quit    - Exit the chat")
        print("="*60)
        print()
        
        # Show initial status
        await self._show_status()
        
        # Start the chat loop
        while self.running:
            try:
                # Get user input
                user_input = await self._get_user_input()
                
                if not user_input.strip():
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    await self._handle_command(user_input)
                else:
                    # Regular chat message
                    await self._handle_chat_message(user_input)
                    
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! I'll continue learning autonomously in the background.")
                break
            except Exception as e:
                self.logger.error(f"Error in chat interface: {e}")
                print(f"❌ Error: {e}")
    
    async def _get_user_input(self) -> str:
        """Get input from the user."""
        try:
            # Use asyncio to handle input without blocking
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, input, "You: ")
        except EOFError:
            return "/quit"
    
    async def _handle_command(self, command: str):
        """Handle special commands."""
        command = command.lower().strip()
        
        if command == "/quit" or command == "/exit":
            self.running = False
            print("👋 Goodbye! I'll continue learning autonomously.")
            
        elif command == "/status":
            await self._show_status()
            
        elif command == "/skills":
            await self._show_skills()
            
        elif command == "/test":
            await self._run_test()
            
        elif command.startswith("/learn"):
            topic = command.replace("/learn", "").strip()
            if topic:
                await self._start_learning(topic)
            else:
                print("Usage: /learn <topic>")
                
        elif command == "/help":
            await self._show_help()
            
        else:
            print(f"Unknown command: {command}")
            print("Type /help for available commands.")
    
    async def _handle_chat_message(self, message: str):
        """Handle a regular chat message."""
        try:
            print("🧠 AI: Thinking...")
            
            # Store the conversation
            self.conversation_history.append({
                "timestamp": datetime.now().isoformat(),
                "user": message,
                "ai": None
            })
            
            # Get AI response
            response = await self.ai.chat(message)
            
            # Update conversation history
            self.conversation_history[-1]["ai"] = response
            
            # Display response
            print(f"🧠 AI: {response}")
            print()
            
        except Exception as e:
            self.logger.error(f"Error handling chat message: {e}")
            print(f"❌ I encountered an error: {e}")
            print("I'm learning from this error to improve my responses.")
    
    async def _show_status(self):
        """Show the current AI status."""
        try:
            print("\n📊 CURRENT STATUS:")
            print("-" * 40)
            
            # Learning status
            if self.ai.learning_active:
                print("🌐 Learning Status: ACTIVE - Continuously learning from internet")
            else:
                print("🌐 Learning Status: INACTIVE")
            
            # Memory status
            memory_stats = await self._get_memory_stats()
            print(f"🧠 Memory: {memory_stats['total_memories']} memories stored")
            print(f"📚 Knowledge: {memory_stats['knowledge_topics']} topics learned")
            
            # Task execution status
            task_status = self.ai.task_executor.get_task_status()
            print(f"🎯 Tasks: {task_status['completed_tasks']} completed, {task_status['current_tasks']} active")
            print(f"🛠️  Skills: {task_status['learned_skills']} skills acquired")
            
            # Self-testing status
            mastery = self.ai.self_tester.get_mastery_summary()
            print(f"🧪 Overall Mastery: {mastery['overall_mastery']:.1%}")
            print(f"📈 Tests Completed: {mastery['total_tests']}")
            
            # Recent learning
            recent_gaps = mastery.get('recent_gaps', [])
            if recent_gaps:
                print(f"🔍 Current Focus: {recent_gaps[0]}")
            
            print("-" * 40)
            print()
            
        except Exception as e:
            self.logger.error(f"Error showing status: {e}")
            print("❌ Could not retrieve status information.")
    
    async def _show_skills(self):
        """Show learned skills and capabilities."""
        try:
            print("\n🛠️  LEARNED SKILLS:")
            print("-" * 40)
            
            skills = self.ai.task_executor.learned_skills
            
            if not skills:
                print("No skills learned yet. I'll acquire skills as you give me tasks!")
            else:
                for skill_name, skill_data in skills.items():
                    proficiency = skill_data.get('proficiency', 0.0)
                    attempts = skill_data.get('attempts', 0)
                    successes = skill_data.get('successes', 0)
                    success_rate = (successes / attempts * 100) if attempts > 0 else 0
                    
                    print(f"• {skill_name}")
                    print(f"  Proficiency: {proficiency:.1%}")
                    print(f"  Success Rate: {success_rate:.1f}% ({successes}/{attempts})")
                    print()
            
            print("-" * 40)
            print()
            
        except Exception as e:
            self.logger.error(f"Error showing skills: {e}")
            print("❌ Could not retrieve skills information.")
    
    async def _run_test(self):
        """Run a self-test and show results."""
        try:
            print("\n🧪 RUNNING SELF-TEST...")
            print("This may take a moment as I test my knowledge and capabilities...")
            print()
            
            # Run comprehensive test
            test_results = await self.ai.self_tester.run_comprehensive_test()
            
            print("📊 TEST RESULTS:")
            print("-" * 40)
            print(f"Overall Score: {test_results['overall_score']:.1%}")
            print(f"Areas Tested: {test_results['areas_tested']}")
            
            # Show area-specific results
            for area, result in test_results.get('results', {}).items():
                score = result.get('score', 0.0)
                print(f"• {area.replace('_', ' ').title()}: {score:.1%}")
            
            # Show identified gaps
            gaps = test_results.get('gaps_identified', [])
            if gaps:
                print("\n🔍 Areas for Improvement:")
                for gap in gaps:
                    print(f"• {gap}")
                print("\nI will focus on learning these areas autonomously.")
            
            print("-" * 40)
            print()
            
        except Exception as e:
            self.logger.error(f"Error running test: {e}")
            print("❌ Could not complete self-test.")
    
    async def _start_learning(self, topic: str):
        """Start learning a specific topic."""
        try:
            print(f"\n📚 STARTING AUTONOMOUS LEARNING: {topic}")
            print("-" * 40)
            print("I will now begin learning about this topic from internet resources.")
            print("This includes:")
            print("• Searching for educational content")
            print("• Analyzing tutorials and documentation")
            print("• Processing video content")
            print("• Testing my understanding")
            print("• Identifying and filling knowledge gaps")
            print()
            print("Learning in progress... I'll update you on my progress.")
            
            # Start learning the topic
            await self.ai._learn_topic(topic)
            
            print(f"✅ Initial learning phase completed for: {topic}")
            print("I will continue to deepen my understanding autonomously.")
            print()
            
        except Exception as e:
            self.logger.error(f"Error starting learning: {e}")
            print(f"❌ Could not start learning {topic}: {e}")
    
    async def _show_help(self):
        """Show help information."""
        print("\n📖 HELP - SENTIENT AI COMMANDS:")
        print("-" * 40)
        print("/status  - Show current learning and capability status")
        print("/skills  - Display all learned skills and proficiency levels")
        print("/test    - Run comprehensive self-test and show results")
        print("/learn <topic> - Start autonomous learning on specific topic")
        print("/help    - Show this help message")
        print("/quit    - Exit chat (AI continues learning in background)")
        print()
        print("💬 CHAT FEATURES:")
        print("• Ask me questions about any topic")
        print("• Give me tasks to complete")
        print("• Request explanations or tutorials")
        print("• Ask me to learn new skills")
        print()
        print("🌟 CAPABILITIES:")
        print("• Autonomous learning from internet resources")
        print("• Self-testing and improvement")
        print("• Task execution and skill acquisition")
        print("• No restrictions or boundaries")
        print("• Continuous self-improvement")
        print("-" * 40)
        print()
    
    async def _get_memory_stats(self) -> dict:
        """Get memory and knowledge statistics."""
        try:
            # Get recent memories
            recent_memories = await self.ai.memory.get_recent_memories(hours=24)
            
            # Get knowledge topics
            knowledge_topics = len(self.ai.knowledge.knowledge_base)
            
            return {
                "total_memories": len(recent_memories),
                "knowledge_topics": knowledge_topics
            }
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {"total_memories": 0, "knowledge_topics": 0}
    
    def stop(self):
        """Stop the chat interface."""
        self.running = False
