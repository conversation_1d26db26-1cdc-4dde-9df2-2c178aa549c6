2025-06-04 14:37:16 - main - INFO - 🧠 Initializing Sentient AI - Autonomous Self-Learning System
2025-06-04 14:37:16 - main - INFO - 🌐 AI will begin teaching itself from internet resources...
2025-06-04 14:37:16 - core.sentient_ai - INFO - 🧠 Initializing Sentient AI core systems...
2025-06-04 14:37:16 - core.memory_system - INFO - 🧠 Initializing memory system...
2025-06-04 14:37:16 - core.memory_system - INFO - Loaded 0 recent memories
2025-06-04 14:37:16 - core.memory_system - INFO - ✅ Memory system initialized
2025-06-04 14:37:16 - core.knowledge_manager - INFO - 📚 Initializing knowledge management system...
2025-06-04 14:37:16 - core.knowledge_manager - INFO - Loaded 0 topics into memory
2025-06-04 14:37:16 - core.knowledge_manager - INFO - ✅ Knowledge management system initialized
2025-06-04 14:37:16 - core.learning_engine - INFO - 🧠 Initializing learning engine...
2025-06-04 14:37:16 - core.learning_engine - INFO - ✅ Learning engine initialized
2025-06-04 14:37:16 - core.task_executor - INFO - 🎯 Initializing task execution engine...
2025-06-04 14:37:16 - core.task_executor - INFO - Available tools: {'web_search': True, 'file_operations': True, 'code_execution': True, 'text_processing': True, 'data_analysis': True}
2025-06-04 14:37:16 - core.task_executor - INFO - ✅ Task execution engine initialized
2025-06-04 14:37:16 - core.self_tester - INFO - 🧪 Initializing self-testing system...
2025-06-04 14:37:16 - core.self_tester - INFO - ✅ Self-testing system initialized
2025-06-04 14:37:16 - core.web_learner - INFO - 🌐 Initializing web learning engine...
2025-06-04 14:37:16 - core.web_learner - INFO - ✅ Web learning engine initialized
2025-06-04 14:37:16 - core.sentient_ai - INFO - 🌐 Starting autonomous learning from internet resources...
2025-06-04 14:37:16 - core.sentient_ai - INFO - 📚 Beginning initial self-teaching phase...
2025-06-04 14:37:16 - core.sentient_ai - INFO - 🎯 Learning: how to learn effectively
2025-06-04 14:37:16 - core.web_learner - INFO - 🔍 Searching for learning resources on: how to learn effectively
2025-06-04 14:37:16 - core.self_tester - INFO - 🔍 Identified 10 knowledge gaps
2025-06-04 14:37:16 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:19 - core.web_learner - INFO - 📚 Found 0 learning resources for how to learn effectively
2025-06-04 14:37:19 - core.self_tester - INFO - 🧪 Testing understanding of: how to learn effectively
2025-06-04 14:37:19 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:19 - core.sentient_ai - INFO - 🔄 Relearning how to learn effectively - test failed
2025-06-04 14:37:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:19 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:19 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:19 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:37:19 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.sentient_ai - INFO - 🎯 Learning: critical thinking and reasoning
2025-06-04 14:37:25 - core.web_learner - INFO - 🔍 Searching for learning resources on: critical thinking and reasoning
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - INFO - 📚 Found 0 learning resources for critical thinking and reasoning
2025-06-04 14:37:27 - core.self_tester - INFO - 🧪 Testing understanding of: critical thinking and reasoning
2025-06-04 14:37:27 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:27 - core.sentient_ai - INFO - 🔄 Relearning critical thinking and reasoning - test failed
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:28 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:28 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:37:28 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.sentient_ai - INFO - 🎯 Learning: problem solving methodologies
2025-06-04 14:37:32 - core.web_learner - INFO - 🔍 Searching for learning resources on: problem solving methodologies
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - INFO - 📚 Found 0 learning resources for problem solving methodologies
2025-06-04 14:37:35 - core.self_tester - INFO - 🧪 Testing understanding of: problem solving methodologies
2025-06-04 14:37:35 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:37:35 - core.sentient_ai - INFO - 🔄 Relearning problem solving methodologies - test failed
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:38 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:38 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:37:38 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.sentient_ai - INFO - 🎯 Learning: research techniques
2025-06-04 14:37:39 - core.web_learner - INFO - 🔍 Searching for learning resources on: research techniques
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - INFO - 📚 Found 0 learning resources for research techniques
2025-06-04 14:37:41 - core.self_tester - INFO - 🧪 Testing understanding of: research techniques
2025-06-04 14:37:41 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:41 - core.sentient_ai - INFO - 🔄 Relearning research techniques - test failed
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:44 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:44 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:37:44 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.sentient_ai - INFO - 🎯 Learning: programming fundamentals
2025-06-04 14:37:47 - core.web_learner - INFO - 🔍 Searching for learning resources on: programming fundamentals
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - INFO - 📚 Found 0 learning resources for programming fundamentals
2025-06-04 14:37:50 - core.self_tester - INFO - 🧪 Testing understanding of: programming fundamentals
2025-06-04 14:37:50 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:50 - core.sentient_ai - INFO - 🔄 Relearning programming fundamentals - test failed
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:51 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:51 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:37:51 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.sentient_ai - INFO - 🎯 Learning: mathematics and logic
2025-06-04 14:37:55 - core.web_learner - INFO - 🔍 Searching for learning resources on: mathematics and logic
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:50 - main - INFO - 🧠 Initializing Sentient AI - Autonomous Self-Learning System
2025-06-04 14:40:50 - main - INFO - 🌐 AI will begin teaching itself from internet resources...
2025-06-04 14:40:50 - core.sentient_ai - INFO - 🧠 Initializing Sentient AI core systems...
2025-06-04 14:40:50 - core.memory_system - INFO - 🧠 Initializing memory system...
2025-06-04 14:40:50 - core.memory_system - INFO - Loaded 0 recent memories
2025-06-04 14:40:50 - core.memory_system - INFO - ✅ Memory system initialized
2025-06-04 14:40:50 - core.knowledge_manager - INFO - 📚 Initializing knowledge management system...
2025-06-04 14:40:50 - core.knowledge_manager - INFO - Loaded 0 topics into memory
2025-06-04 14:40:50 - core.knowledge_manager - INFO - ✅ Knowledge management system initialized
2025-06-04 14:40:50 - core.learning_engine - INFO - 🧠 Initializing learning engine...
2025-06-04 14:40:50 - core.learning_engine - INFO - ✅ Learning engine initialized
2025-06-04 14:40:50 - core.task_executor - INFO - 🎯 Initializing task execution engine...
2025-06-04 14:40:50 - core.task_executor - INFO - Available tools: {'web_search': True, 'file_operations': True, 'code_execution': True, 'text_processing': True, 'data_analysis': True}
2025-06-04 14:40:50 - core.task_executor - INFO - ✅ Task execution engine initialized
2025-06-04 14:40:50 - core.self_tester - INFO - 🧪 Initializing self-testing system...
2025-06-04 14:40:50 - core.self_tester - INFO - Loaded 10 test records
2025-06-04 14:40:50 - core.self_tester - INFO - ✅ Self-testing system initialized
2025-06-04 14:40:50 - core.web_learner - INFO - 🌐 Initializing web learning engine...
2025-06-04 14:40:50 - core.web_learner - INFO - ✅ Web learning engine initialized
2025-06-04 14:40:50 - core.sentient_ai - INFO - 🌐 Starting autonomous learning from internet resources...
2025-06-04 14:40:50 - core.sentient_ai - INFO - 📚 Beginning initial self-teaching phase...
2025-06-04 14:40:50 - core.sentient_ai - INFO - 🎯 Learning: how to learn effectively
2025-06-04 14:40:50 - core.web_learner - INFO - 🔍 Searching for learning resources on: how to learn effectively
2025-06-04 14:40:50 - core.self_tester - INFO - 🔍 Identified 16 knowledge gaps
2025-06-04 14:40:50 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:40:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:54 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:40:54 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:40:54 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:40:54 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:40:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:54 - core.web_learner - INFO - 📚 Found 0 learning resources for how to learn effectively
2025-06-04 14:40:54 - core.self_tester - INFO - 🧪 Testing understanding of: how to learn effectively
2025-06-04 14:40:54 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:40:54 - core.sentient_ai - INFO - 🔄 Relearning how to learn effectively - test failed
2025-06-04 14:40:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:40:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:00 - core.sentient_ai - INFO - 🎯 Learning: critical thinking and reasoning
2025-06-04 14:41:00 - core.web_learner - INFO - 🔍 Searching for learning resources on: critical thinking and reasoning
2025-06-04 14:41:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:02 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:03 - core.web_learner - INFO - 📚 Found 0 learning resources for critical thinking and reasoning
2025-06-04 14:41:03 - core.self_tester - INFO - 🧪 Testing understanding of: critical thinking and reasoning
2025-06-04 14:41:03 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:41:03 - core.sentient_ai - INFO - 🔄 Relearning critical thinking and reasoning - test failed
2025-06-04 14:41:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:05 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:05 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:05 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:41:05 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:41:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:11 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:11 - core.sentient_ai - INFO - 🎯 Learning: problem solving methodologies
2025-06-04 14:41:11 - core.web_learner - INFO - 🔍 Searching for learning resources on: problem solving methodologies
2025-06-04 14:41:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:14 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:14 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:14 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:41:14 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:41:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:14 - core.web_learner - INFO - 📚 Found 0 learning resources for problem solving methodologies
2025-06-04 14:41:14 - core.self_tester - INFO - 🧪 Testing understanding of: problem solving methodologies
2025-06-04 14:41:14 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:41:14 - core.sentient_ai - INFO - 🔄 Relearning problem solving methodologies - test failed
2025-06-04 14:41:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:19 - core.web_learner - INFO - 🔍 Searching for learning resources on: I understand you want me to perform a task. I would need to learn the specific steps and requirements first.
2025-06-04 14:41:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:21 - core.sentient_ai - INFO - 🎯 Learning: research techniques
2025-06-04 14:41:21 - core.web_learner - INFO - 🔍 Searching for learning resources on: research techniques
2025-06-04 14:41:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:22 - core.web_learner - INFO - 📚 Found 0 learning resources for I understand you want me to perform a task. I would need to learn the specific steps and requirements first.
2025-06-04 14:41:22 - core.self_tester - INFO - 🧪 Testing understanding of: I understand you want me to perform a task. I would need to learn the specific steps and requirements first.
2025-06-04 14:41:22 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.00, Passed: False
2025-06-04 14:41:22 - core.sentient_ai - INFO - 🔄 Relearning I understand you want me to perform a task. I would need to learn the specific steps and requirements first. - test failed
2025-06-04 14:41:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:23 - core.web_learner - INFO - 📚 Found 0 learning resources for research techniques
2025-06-04 14:41:23 - core.self_tester - INFO - 🧪 Testing understanding of: research techniques
2025-06-04 14:41:23 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:41:23 - core.sentient_ai - INFO - 🔄 Relearning research techniques - test failed
2025-06-04 14:41:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:30 - core.sentient_ai - INFO - 🎯 Learning: programming fundamentals
2025-06-04 14:41:30 - core.web_learner - INFO - 🔍 Searching for learning resources on: programming fundamentals
2025-06-04 14:41:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:31 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:33 - core.web_learner - INFO - 📚 Found 0 learning resources for programming fundamentals
2025-06-04 14:41:33 - core.self_tester - INFO - 🧪 Testing understanding of: programming fundamentals
2025-06-04 14:41:33 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:41:33 - core.sentient_ai - INFO - 🔄 Relearning programming fundamentals - test failed
2025-06-04 14:41:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:34 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:34 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:34 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:41:34 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:41:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:40 - core.sentient_ai - INFO - 🎯 Learning: mathematics and logic
2025-06-04 14:41:40 - core.web_learner - INFO - 🔍 Searching for learning resources on: mathematics and logic
2025-06-04 14:41:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:42 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:43 - core.web_learner - INFO - 📚 Found 0 learning resources for mathematics and logic
2025-06-04 14:41:43 - core.self_tester - INFO - 🧪 Testing understanding of: mathematics and logic
2025-06-04 14:41:43 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:41:43 - core.sentient_ai - INFO - 🔄 Relearning mathematics and logic - test failed
2025-06-04 14:41:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:45 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:45 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:45 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:41:45 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:41:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:49 - core.sentient_ai - INFO - 🎯 Learning: science and technology
2025-06-04 14:41:49 - core.web_learner - INFO - 🔍 Searching for learning resources on: science and technology
2025-06-04 14:41:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:52 - core.web_learner - INFO - 📚 Found 0 learning resources for science and technology
2025-06-04 14:41:52 - core.self_tester - INFO - 🧪 Testing understanding of: science and technology
2025-06-04 14:41:52 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:41:52 - core.sentient_ai - INFO - 🔄 Relearning science and technology - test failed
2025-06-04 14:41:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:54 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:57 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:57 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:41:57 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:41:57 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:41:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:41:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:00 - core.sentient_ai - INFO - 🎯 Learning: communication and language
2025-06-04 14:42:00 - core.web_learner - INFO - 🔍 Searching for learning resources on: communication and language
2025-06-04 14:42:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:03 - core.web_learner - INFO - 📚 Found 0 learning resources for communication and language
2025-06-04 14:42:03 - core.self_tester - INFO - 🧪 Testing understanding of: communication and language
2025-06-04 14:42:03 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:42:03 - core.sentient_ai - INFO - 🔄 Relearning communication and language - test failed
2025-06-04 14:42:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:03 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:06 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:06 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:06 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:42:06 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:42:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.sentient_ai - INFO - 🎯 Learning: self-improvement strategies
2025-06-04 14:42:12 - core.web_learner - INFO - 🔍 Searching for learning resources on: self-improvement strategies
2025-06-04 14:42:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:12 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:15 - core.web_learner - INFO - 📚 Found 0 learning resources for self-improvement strategies
2025-06-04 14:42:15 - core.self_tester - INFO - 🧪 Testing understanding of: self-improvement strategies
2025-06-04 14:42:15 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:42:15 - core.sentient_ai - INFO - 🔄 Relearning self-improvement strategies - test failed
2025-06-04 14:42:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:15 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:15 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:15 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:42:15 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:42:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:22 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:23 - core.sentient_ai - INFO - 🎯 Learning: goal setting and planning
2025-06-04 14:42:23 - core.web_learner - INFO - 🔍 Searching for learning resources on: goal setting and planning
2025-06-04 14:42:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:25 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:25 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:25 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.00, Passed: False
2025-06-04 14:42:25 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:42:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:25 - core.web_learner - INFO - 📚 Found 0 learning resources for goal setting and planning
2025-06-04 14:42:25 - core.self_tester - INFO - 🧪 Testing understanding of: goal setting and planning
2025-06-04 14:42:25 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:42:25 - core.sentient_ai - INFO - 🔄 Relearning goal setting and planning - test failed
2025-06-04 14:42:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:32 - core.sentient_ai - INFO - ✅ Sentient AI initialized and ready
2025-06-04 14:42:32 - main - INFO - ✅ Sentient AI is now active and learning autonomously
2025-06-04 14:42:32 - main - INFO - 💬 Chat interface ready - you can now interact with the AI
2025-06-04 14:42:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:34 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:37 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:37 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:37 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:42:37 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:42:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:45 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:48 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:48 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:48 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:42:48 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:42:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:53 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:56 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:56 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:42:56 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:42:56 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:42:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:42:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:04 - core.web_learner - INFO - 🔍 Searching for learning resources on: I understand you want me to perform a task. I would need to learn the specific steps and requirements first.
2025-06-04 14:43:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:07 - core.web_learner - INFO - 📚 Found 0 learning resources for I understand you want me to perform a task. I would need to learn the specific steps and requirements first.
2025-06-04 14:43:07 - core.self_tester - INFO - 🧪 Testing understanding of: I understand you want me to perform a task. I would need to learn the specific steps and requirements first.
2025-06-04 14:43:07 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:43:07 - core.sentient_ai - INFO - 🔄 Relearning I understand you want me to perform a task. I would need to learn the specific steps and requirements first. - test failed
2025-06-04 14:43:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:08 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:09 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:10 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:11 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:12 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:13 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:14 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:15 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:43:15 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:16 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:18 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:43:18 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:43:18 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:43:18 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:43:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:26 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:43:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:29 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:43:29 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:43:29 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:43:29 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:43:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:35 - core.web_learner - INFO - 🔍 Searching for learning resources on: Explore emerging technologies and trends to expand understanding and capabilities
2025-06-04 14:43:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:38 - core.web_learner - INFO - 📚 Found 0 learning resources for Explore emerging technologies and trends to expand understanding and capabilities
2025-06-04 14:43:38 - core.self_tester - INFO - 🧪 Testing understanding of: Explore emerging technologies and trends to expand understanding and capabilities
2025-06-04 14:43:38 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:43:38 - core.sentient_ai - INFO - 🔄 Relearning Explore emerging technologies and trends to expand understanding and capabilities - test failed
2025-06-04 14:43:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:48 - core.web_learner - INFO - 🔍 Searching for learning resources on: Explore advanced problem-solving techniques to expand understanding and capabilities
2025-06-04 14:43:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:51 - core.web_learner - INFO - 📚 Found 0 learning resources for Explore advanced problem-solving techniques to expand understanding and capabilities
2025-06-04 14:43:51 - core.self_tester - INFO - 🧪 Testing understanding of: Explore advanced problem-solving techniques to expand understanding and capabilities
2025-06-04 14:43:51 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:43:51 - core.sentient_ai - INFO - 🔄 Relearning Explore advanced problem-solving techniques to expand understanding and capabilities - test failed
2025-06-04 14:43:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:57 - core.web_learner - INFO - 🔍 Searching for learning resources on: Explore creative thinking methods to expand understanding and capabilities
2025-06-04 14:43:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:58 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:43:59 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:00 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:00 - core.web_learner - INFO - 📚 Found 0 learning resources for Explore creative thinking methods to expand understanding and capabilities
2025-06-04 14:44:00 - core.self_tester - INFO - 🧪 Testing understanding of: Explore creative thinking methods to expand understanding and capabilities
2025-06-04 14:44:00 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:44:00 - core.sentient_ai - INFO - 🔄 Relearning Explore creative thinking methods to expand understanding and capabilities - test failed
2025-06-04 14:44:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:01 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:02 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:03 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:04 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:05 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:06 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:07 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:44:07 - core.self_tester - INFO - 🧪 Running comprehensive knowledge test...
2025-06-04 14:44:07 - core.self_tester - INFO - ✅ Comprehensive test completed. Overall score: 0.66
2025-06-04 14:44:07 - core.knowledge_manager - INFO - 📊 Knowledge consolidation completed
2025-06-04 14:45:39 - core.sentient_ai - INFO - 🎯 Received task: user_request
2025-06-04 14:45:39 - core.task_executor - INFO - 🎯 Executing task: user_request (category: general)
2025-06-04 14:45:39 - core.task_executor - INFO - ✅ Task completed: user_request
2025-06-04 14:45:54 - core.learning_engine - ERROR - Error generating response: 'speaker'
2025-06-04 14:46:24 - core.learning_engine - ERROR - Error generating response: 'speaker'
