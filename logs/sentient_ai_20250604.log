2025-06-04 14:37:16 - main - INFO - 🧠 Initializing Sentient AI - Autonomous Self-Learning System
2025-06-04 14:37:16 - main - INFO - 🌐 AI will begin teaching itself from internet resources...
2025-06-04 14:37:16 - core.sentient_ai - INFO - 🧠 Initializing Sentient AI core systems...
2025-06-04 14:37:16 - core.memory_system - INFO - 🧠 Initializing memory system...
2025-06-04 14:37:16 - core.memory_system - INFO - Loaded 0 recent memories
2025-06-04 14:37:16 - core.memory_system - INFO - ✅ Memory system initialized
2025-06-04 14:37:16 - core.knowledge_manager - INFO - 📚 Initializing knowledge management system...
2025-06-04 14:37:16 - core.knowledge_manager - INFO - Loaded 0 topics into memory
2025-06-04 14:37:16 - core.knowledge_manager - INFO - ✅ Knowledge management system initialized
2025-06-04 14:37:16 - core.learning_engine - INFO - 🧠 Initializing learning engine...
2025-06-04 14:37:16 - core.learning_engine - INFO - ✅ Learning engine initialized
2025-06-04 14:37:16 - core.task_executor - INFO - 🎯 Initializing task execution engine...
2025-06-04 14:37:16 - core.task_executor - INFO - Available tools: {'web_search': True, 'file_operations': True, 'code_execution': True, 'text_processing': True, 'data_analysis': True}
2025-06-04 14:37:16 - core.task_executor - INFO - ✅ Task execution engine initialized
2025-06-04 14:37:16 - core.self_tester - INFO - 🧪 Initializing self-testing system...
2025-06-04 14:37:16 - core.self_tester - INFO - ✅ Self-testing system initialized
2025-06-04 14:37:16 - core.web_learner - INFO - 🌐 Initializing web learning engine...
2025-06-04 14:37:16 - core.web_learner - INFO - ✅ Web learning engine initialized
2025-06-04 14:37:16 - core.sentient_ai - INFO - 🌐 Starting autonomous learning from internet resources...
2025-06-04 14:37:16 - core.sentient_ai - INFO - 📚 Beginning initial self-teaching phase...
2025-06-04 14:37:16 - core.sentient_ai - INFO - 🎯 Learning: how to learn effectively
2025-06-04 14:37:16 - core.web_learner - INFO - 🔍 Searching for learning resources on: how to learn effectively
2025-06-04 14:37:16 - core.self_tester - INFO - 🔍 Identified 10 knowledge gaps
2025-06-04 14:37:16 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:17 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:18 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:19 - core.web_learner - INFO - 📚 Found 0 learning resources for how to learn effectively
2025-06-04 14:37:19 - core.self_tester - INFO - 🧪 Testing understanding of: how to learn effectively
2025-06-04 14:37:19 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:19 - core.sentient_ai - INFO - 🔄 Relearning how to learn effectively - test failed
2025-06-04 14:37:19 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:19 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:19 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:19 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:37:19 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:20 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:21 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:22 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:23 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:24 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.sentient_ai - INFO - 🎯 Learning: critical thinking and reasoning
2025-06-04 14:37:25 - core.web_learner - INFO - 🔍 Searching for learning resources on: critical thinking and reasoning
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:25 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:26 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - INFO - 📚 Found 0 learning resources for critical thinking and reasoning
2025-06-04 14:37:27 - core.self_tester - INFO - 🧪 Testing understanding of: critical thinking and reasoning
2025-06-04 14:37:27 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:27 - core.sentient_ai - INFO - 🔄 Relearning critical thinking and reasoning - test failed
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:27 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:28 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:28 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:28 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:37:28 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:29 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:30 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:31 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:32 - core.sentient_ai - INFO - 🎯 Learning: problem solving methodologies
2025-06-04 14:37:32 - core.web_learner - INFO - 🔍 Searching for learning resources on: problem solving methodologies
2025-06-04 14:37:32 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:33 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:34 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - INFO - 📚 Found 0 learning resources for problem solving methodologies
2025-06-04 14:37:35 - core.self_tester - INFO - 🧪 Testing understanding of: problem solving methodologies
2025-06-04 14:37:35 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:37:35 - core.sentient_ai - INFO - 🔄 Relearning problem solving methodologies - test failed
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:35 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:36 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:37 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:38 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:38 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:37:38 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:38 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.sentient_ai - INFO - 🎯 Learning: research techniques
2025-06-04 14:37:39 - core.web_learner - INFO - 🔍 Searching for learning resources on: research techniques
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:39 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:40 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - INFO - 📚 Found 0 learning resources for research techniques
2025-06-04 14:37:41 - core.self_tester - INFO - 🧪 Testing understanding of: research techniques
2025-06-04 14:37:41 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:41 - core.sentient_ai - INFO - 🔄 Relearning research techniques - test failed
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:41 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:42 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:43 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:44 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:44 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.50, Passed: False
2025-06-04 14:37:44 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:44 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:45 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:46 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.sentient_ai - INFO - 🎯 Learning: programming fundamentals
2025-06-04 14:37:47 - core.web_learner - INFO - 🔍 Searching for learning resources on: programming fundamentals
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:47 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:48 - core.web_learner - INFO - 🔍 Searching for learning resources on: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:48 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:49 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - INFO - 📚 Found 0 learning resources for programming fundamentals
2025-06-04 14:37:50 - core.self_tester - INFO - 🧪 Testing understanding of: programming fundamentals
2025-06-04 14:37:50 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.33, Passed: False
2025-06-04 14:37:50 - core.sentient_ai - INFO - 🔄 Relearning programming fundamentals - test failed
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:50 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - INFO - 📚 Found 0 learning resources for I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:51 - core.self_tester - INFO - 🧪 Testing understanding of: I'm processing your request and will learn more about this topic to provide better responses in the future.
2025-06-04 14:37:51 - core.self_tester - INFO - 📊 Topic test completed. Score: 0.17, Passed: False
2025-06-04 14:37:51 - core.sentient_ai - INFO - 🔄 Relearning I'm processing your request and will learn more about this topic to provide better responses in the future. - test failed
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:51 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:52 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:53 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:54 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.sentient_ai - INFO - 🎯 Learning: mathematics and logic
2025-06-04 14:37:55 - core.web_learner - INFO - 🔍 Searching for learning resources on: mathematics and logic
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:55 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:56 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
2025-06-04 14:37:57 - core.web_learner - ERROR - Error searching web: Cannot connect to host duckduckgo.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1000)')]
