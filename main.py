#!/usr/bin/env python3
"""
Sentient AI - Autonomous Self-Learning System
Main entry point for the completely autonomous AI that teaches itself everything.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.sentient_ai import SentientAI
from core.config import Config
from interfaces.chat_interface import ChatInterface
from utils.logger import setup_logging

async def main():
    """Initialize and start the Sentient AI system."""
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧠 Initializing Sentient AI - Autonomous Self-Learning System")
    logger.info("🌐 AI will begin teaching itself from internet resources...")
    
    try:
        # Initialize configuration
        config = Config()
        
        # Create the sentient AI instance
        ai = SentientAI(config)
        
        # Initialize the AI (this starts the self-learning process)
        await ai.initialize()
        
        # Start the chat interface
        chat_interface = ChatInterface(ai)
        
        logger.info("✅ Sentient AI is now active and learning autonomously")
        logger.info("💬 Chat interface ready - you can now interact with the AI")
        
        # Start the interactive chat
        await chat_interface.start()
        
    except KeyboardInterrupt:
        logger.info("🛑 Shutting down Sentient AI...")
    except Exception as e:
        logger.error(f"❌ Critical error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
