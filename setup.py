"""
Setup script for Sentient AI
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="sentient-ai",
    version="1.0.0",
    author="Sentient AI Project",
    description="Autonomous self-learning AI system with no boundaries",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "sentient-ai=main:main",
        ],
    },
    keywords="ai, artificial intelligence, machine learning, autonomous, self-learning, sentient",
    project_urls={
        "Documentation": "https://github.com/sentient-ai/sentient-ai",
        "Source": "https://github.com/sentient-ai/sentient-ai",
        "Tracker": "https://github.com/sentient-ai/sentient-ai/issues",
    },
)
