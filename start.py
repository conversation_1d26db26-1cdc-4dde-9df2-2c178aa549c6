#!/usr/bin/env python3
"""
Quick start script for Sentient AI
This script provides an easy way to launch the autonomous AI system.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Main entry point for starting Sentient AI."""
    
    print("🧠 SENTIENT AI - AUTONOMOUS LEARNING SYSTEM")
    print("=" * 50)
    print("Initializing completely autonomous AI that learns from the internet...")
    print("The AI will begin teaching itself immediately upon startup.")
    print("=" * 50)
    print()
    
    try:
        # Import and run the main application
        from main import main as run_main
        asyncio.run(run_main())
        
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down Sentient AI...")
        print("The AI has been stopped but will resume learning when restarted.")
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\nPlease install required dependencies:")
        print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Error starting Sentient AI: {e}")
        print("\nCheck the logs for more details.")

if __name__ == "__main__":
    main()
