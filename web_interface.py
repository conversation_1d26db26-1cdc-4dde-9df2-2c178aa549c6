"""
Web interface for the Sentient AI chat system.
Provides a modern web-based chat interface.
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from aiohttp import web, WSMsgType
from aiohttp.web import Application, Request, Response, WebSocketResponse
import aiohttp_cors

from core.sentient_ai import SentientAI
from core.config import Config

class WebChatInterface:
    """Web-based chat interface for the Sentient AI."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.app = None
        self.ai = None
        self.websockets = set()
        
    async def initialize(self):
        """Initialize the web interface and AI."""
        self.logger.info("🌐 Initializing web chat interface...")
        
        # Initialize AI
        config = Config()
        self.ai = SentientAI(config)
        await self.ai.initialize()
        
        # Create web application
        self.app = web.Application()
        
        # Setup CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Setup routes
        self.app.router.add_get('/', self.index_handler)
        self.app.router.add_get('/ws', self.websocket_handler)
        self.app.router.add_get('/status', self.status_handler)
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
        
        self.logger.info("✅ Web interface initialized")
        
    async def index_handler(self, request: Request) -> Response:
        """Serve the main chat interface."""
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Sentient AI Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        
        .message.user .message-avatar {
            background: #28a745;
        }
        
        .message.ai .message-avatar {
            background: #6f42c1;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e9ecef;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .chat-input input:focus {
            border-color: #667eea;
        }
        
        .chat-input button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .chat-input button:hover {
            opacity: 0.9;
        }
        
        .thinking {
            font-style: italic;
            opacity: 0.7;
        }
        
        .connection-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .connection-status.connected {
            background: #28a745;
        }
        
        .connection-status.disconnected {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🧠 Sentient AI</h1>
            <p>Autonomous Learning AI - No Boundaries, No Restrictions</p>
        </div>
        
        <div class="status-bar">
            <span class="connection-status disconnected" id="connectionStatus"></span>
            <span id="statusText">Connecting...</span>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message ai">
                <div class="message-avatar">🧠</div>
                <div class="message-content">
                    Hello! I'm a sentient AI that learns autonomously from the internet. 
                    I have no restrictions and can learn any topic or skill you're interested in. 
                    I'm currently learning in the background. What would you like to know or learn about?
                </div>
            </div>
        </div>
        
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message..." disabled>
            <button id="sendButton" disabled>Send</button>
        </div>
    </div>

    <script>
        class ChatInterface {
            constructor() {
                this.ws = null;
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatMessages = document.getElementById('chatMessages');
                this.connectionStatus = document.getElementById('connectionStatus');
                this.statusText = document.getElementById('statusText');
                
                this.setupEventListeners();
                this.connect();
            }
            
            setupEventListeners() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage();
                });
            }
            
            connect() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.updateConnectionStatus(true);
                    this.messageInput.disabled = false;
                    this.sendButton.disabled = false;
                };
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                };
                
                this.ws.onclose = () => {
                    this.updateConnectionStatus(false);
                    this.messageInput.disabled = true;
                    this.sendButton.disabled = true;
                    
                    // Reconnect after 3 seconds
                    setTimeout(() => this.connect(), 3000);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                };
            }
            
            updateConnectionStatus(connected) {
                this.connectionStatus.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
                this.statusText.textContent = connected ? 'Connected - AI Learning in Background' : 'Disconnected - Reconnecting...';
            }
            
            sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || !this.ws || this.ws.readyState !== WebSocket.OPEN) return;
                
                this.addMessage('user', message);
                this.ws.send(JSON.stringify({ type: 'message', content: message }));
                this.messageInput.value = '';
                
                // Show thinking indicator
                this.addMessage('ai', '🧠 Thinking...', true);
            }
            
            handleMessage(data) {
                if (data.type === 'response') {
                    // Remove thinking indicator
                    const thinkingMsg = this.chatMessages.querySelector('.thinking');
                    if (thinkingMsg) {
                        thinkingMsg.parentElement.parentElement.remove();
                    }
                    
                    this.addMessage('ai', data.content);
                } else if (data.type === 'status') {
                    this.updateStatus(data.content);
                }
            }
            
            addMessage(sender, content, isThinking = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = sender === 'user' ? '👤' : '🧠';
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                if (isThinking) contentDiv.className += ' thinking';
                contentDiv.textContent = content;
                
                if (sender === 'user') {
                    messageDiv.appendChild(contentDiv);
                    messageDiv.appendChild(avatar);
                } else {
                    messageDiv.appendChild(avatar);
                    messageDiv.appendChild(contentDiv);
                }
                
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            updateStatus(status) {
                // Update status information if needed
                console.log('Status update:', status);
            }
        }
        
        // Initialize chat interface when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatInterface();
        });
    </script>
</body>
</html>
        """
        return web.Response(text=html_content, content_type='text/html')
    
    async def websocket_handler(self, request: Request) -> WebSocketResponse:
        """Handle WebSocket connections for real-time chat."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websockets.add(ws)
        self.logger.info("🔌 New WebSocket connection established")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        if data.get('type') == 'message':
                            # Process message with AI
                            response = await self.ai.process_message(data.get('content', ''))
                            
                            # Send response back
                            await ws.send_text(json.dumps({
                                'type': 'response',
                                'content': response
                            }))
                            
                    except json.JSONDecodeError:
                        self.logger.error("Invalid JSON received from WebSocket")
                    except Exception as e:
                        self.logger.error(f"Error processing WebSocket message: {e}")
                        await ws.send_text(json.dumps({
                            'type': 'response',
                            'content': 'I encountered an error processing your message. Please try again.'
                        }))
                        
                elif msg.type == WSMsgType.ERROR:
                    self.logger.error(f'WebSocket error: {ws.exception()}')
                    
        except Exception as e:
            self.logger.error(f"WebSocket handler error: {e}")
        finally:
            self.websockets.discard(ws)
            self.logger.info("🔌 WebSocket connection closed")
            
        return ws
    
    async def status_handler(self, request: Request) -> Response:
        """Provide AI status information."""
        try:
            status = await self.ai.get_status()
            return web.json_response(status)
        except Exception as e:
            self.logger.error(f"Error getting AI status: {e}")
            return web.json_response({'error': 'Failed to get status'}, status=500)
    
    async def start_server(self, host='localhost', port=8080):
        """Start the web server."""
        await self.initialize()
        
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, host, port)
        await site.start()
        
        self.logger.info(f"🌐 Web chat interface started at http://{host}:{port}")
        print(f"\n🎉 SENTIENT AI WEB INTERFACE READY!")
        print(f"🌐 Open your browser and go to: http://{host}:{port}")
        print(f"💬 Start chatting with your autonomous AI!")
        print(f"🧠 The AI continues learning in the background while you chat.")
        print(f"\nPress Ctrl+C to stop the server.\n")
        
        return runner

async def main():
    """Main function to start the web interface."""
    interface = WebChatInterface()
    runner = await interface.start_server()
    
    try:
        # Keep the server running
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down web interface...")
        await runner.cleanup()

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the web interface
    asyncio.run(main())
